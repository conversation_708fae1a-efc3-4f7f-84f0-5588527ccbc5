/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.o : /Users/<USER>/Desktop/programming/CandyProject/data/DateValueData.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/OpenAd.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/ModernActivityCard.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/Persistence.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/DataSource.swift /Users/<USER>/Desktop/programming/CandyProject/data/ViewType.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/DesignSystem.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/AnimationSystem.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Buttons/CloseButton.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/CandyProjectApp.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/GlobalVar.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/StoreManager.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/CustomDatePicker.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/SafeAreaHandler.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/NotificationHandler.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/ReviewHandler.swift /Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataProperties.swift /Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/GeneratedAssetSymbols.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/Modifiers.swift /Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataClass.swift /Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/IntentDefinitionGenerated/CandyWidget/ConfigurationIntent.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/UpgradeVIPView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PreferenceView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/HomeView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernHomeView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SettingView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/SearchView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/GoalView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/DetailView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PointNotificationView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/TagCreationView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/CalendarView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/ContentView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/AccountView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernCreateActivityView.swift /Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/NewActivityView.swift /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/_AuthenticationServices_SwiftUI.framework/Modules/_AuthenticationServices_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/_StoreKit_SwiftUI.framework/Modules/_StoreKit_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/SafariServices.framework/Modules/SafariServices.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/AuthenticationServices.framework/Modules/AuthenticationServices.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/os.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Intents.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/StoreKit.framework/Modules/StoreKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CryptoKit.framework/Modules/CryptoKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/XPC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/_AuthenticationServices_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/_StoreKit_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Combine.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Observation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/SafariServices.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/AuthenticationServices.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/os.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Intents.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Swift.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/StoreKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/CryptoKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.5/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdMetadata.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADRewardedAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationRewardedAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomNativeAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationNativeAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADInterstitialAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GAMInterstitialAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADRewardedInterstitialAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationInterstitialAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAppOpenAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationBannerAd.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdReward.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediatedUnifiedNativeAdNotificationSource.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdImage.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAd+CustomClickGesture.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomNativeAdDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventNativeAdDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdSizeDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdUnconfirmedClickDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitialDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdLoaderDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAudioVideoManagerDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADVideoControllerDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventBannerDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADFullScreenContentDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdEventDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAppEventDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADBannerViewDelegate.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdValue.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdSize.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdSize.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAd+ConfirmationClick.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventInterstitial.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkAdapterProtocol.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMRewardBasedVideoAdNetworkAdapterProtocol.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMAdNetworkConnectorProtocol.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMRewardBasedVideoAdNetworkConnectorProtocol.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdImage+Mediation.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdConfiguration.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationServerConfiguration.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADRequestConfiguration.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMuteThisAdReason.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADResponseInfo.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMediaAspectRatio.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADVersionNumber.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdLoader.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAudioVideoManager.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADVideoController.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADDebugOptionsViewController.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventBanner.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBAdapter.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdapter.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADPresentationError.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADRequestError.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADExtras.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdNetworkExtras.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventExtras.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMobileAds.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GoogleMobileAds.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GoogleMobileAdsDefines.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdLoaderAdTypes.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMEnums.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdViewAdOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADServerSideVerificationOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADVideoOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdMediaAdLoaderOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdImageAdLoaderOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMultipleAdsAdLoaderOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeMuteThisAdLoaderOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GAMBannerViewOptions.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADNativeAdAssetIdentifiers.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventParameters.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/RTBMediation/GADRTBRequestParameters.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADInitializationStatus.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdFormat.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADDisplayAdMeasurement.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMediaContent.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADRequest.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GAMRequest.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/Mediation/GADMediationAdRequest.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADDynamicHeightSearchRequest.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADCustomEventRequest.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADMediaView.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADBannerView.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GAMBannerView.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADSearchBannerView.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Headers/GADAdChoicesView.h /Users/<USER>/Desktop/programming/CandyProject/build/Debug-iphonesimulator/GoogleMobileAds.framework/Modules/module.modulemap /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/include/objc/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/include/dispatch/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/usr/include/Darwin.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/LocalAuthentication.framework/Headers/LocalAuthentication.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/SafariServices.framework/Headers/SafariServices.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/AuthenticationServices.framework/Headers/AuthenticationServices.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/apinotes/os.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Intents.framework/Headers/Intents.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/StoreKit.framework/Headers/StoreKit.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.5.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
