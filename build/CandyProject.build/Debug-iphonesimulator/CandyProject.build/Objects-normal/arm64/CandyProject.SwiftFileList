/Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/DetailView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/TagCreationView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/Modifiers.swift
/Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Buttons/CloseButton.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/OpenAd.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/CalendarView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/AccountView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/DataSource.swift
/Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/HomeView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernHomeView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernCreateActivityView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/DesignSystem.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/AnimationSystem.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/SafeAreaHandler.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/ModernActivityCard.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/GoalView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/UpgradeVIPView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PointNotificationView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/data/ViewType.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/CustomDatePicker.swift
/Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/GlobalVar.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/ReviewHandler.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/StoreManager.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/SearchView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/ContentView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/data/DateValueData.swift
/Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/Persistence.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/CandyProjectApp.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SettingView.swift
/Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataClass.swift
/Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/NewActivityView.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/NotificationHandler.swift
/Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataProperties.swift
/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PreferenceView.swift
/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/IntentDefinitionGenerated/CandyWidget/ConfigurationIntent.swift
/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/GeneratedAssetSymbols.swift
