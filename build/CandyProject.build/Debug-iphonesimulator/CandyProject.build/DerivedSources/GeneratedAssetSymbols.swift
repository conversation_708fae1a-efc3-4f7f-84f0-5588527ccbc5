import Foundation
#if canImport(AppKit)
import AppKit
#endif
#if canImport(UIKit)
import UIKit
#endif
#if canImport(SwiftUI)
import Swift<PERSON>
#endif
#if canImport(DeveloperToolsSupport)
import DeveloperToolsSupport
#endif

#if SWIFT_PACKAGE
private let resourceBundle = Foundation.Bundle.module
#else
private class ResourceBundleClass {}
private let resourceBundle = Foundation.Bundle(for: ResourceBundleClass.self)
#endif

// MARK: - Color Symbols -

@available(iOS 11.0, macOS 10.13, tvOS 11.0, *)
extension ColorResource {

    /// The "AccentColor" asset catalog color resource.
    static let accent = ColorResource(name: "AccentColor", bundle: resourceBundle)

    /// The "blue" asset catalog color resource.
    static let blue = ColorResource(name: "blue", bundle: resourceBundle)

    /// The "brightgreen" asset catalog color resource.
    static let brightgreen = ColorResource(name: "brightgreen", bundle: resourceBundle)

    /// The "darkgreen" asset catalog color resource.
    static let darkgreen = ColorResource(name: "darkgreen", bundle: resourceBundle)

    /// The "darkred" asset catalog color resource.
    static let darkred = ColorResource(name: "darkred", bundle: resourceBundle)

    /// The "green" asset catalog color resource.
    static let green = ColorResource(name: "green", bundle: resourceBundle)

    /// The "lightgray" asset catalog color resource.
    static let lightgray = ColorResource(name: "lightgray", bundle: resourceBundle)

    /// The "lightpink" asset catalog color resource.
    static let lightpink = ColorResource(name: "lightpink", bundle: resourceBundle)

    /// The "lightyellow" asset catalog color resource.
    static let lightyellow = ColorResource(name: "lightyellow", bundle: resourceBundle)

    /// The "orange" asset catalog color resource.
    static let orange = ColorResource(name: "orange", bundle: resourceBundle)

    /// The "pink" asset catalog color resource.
    static let pink = ColorResource(name: "pink", bundle: resourceBundle)

    /// The "purple" asset catalog color resource.
    static let purple = ColorResource(name: "purple", bundle: resourceBundle)

    /// The "red" asset catalog color resource.
    static let red = ColorResource(name: "red", bundle: resourceBundle)

    /// The "yellow" asset catalog color resource.
    static let yellow = ColorResource(name: "yellow", bundle: resourceBundle)

}

// MARK: - Image Symbols -

@available(iOS 11.0, macOS 10.7, tvOS 11.0, *)
extension ImageResource {

    /// The "add 1" asset catalog image resource.
    static let add1 = ImageResource(name: "add 1", bundle: resourceBundle)

    /// The "bg" asset catalog image resource.
    static let bg = ImageResource(name: "bg", bundle: resourceBundle)

    /// The "bg2" asset catalog image resource.
    static let bg2 = ImageResource(name: "bg2", bundle: resourceBundle)

    /// The "bg3" asset catalog image resource.
    static let bg3 = ImageResource(name: "bg3", bundle: resourceBundle)

    /// The "bg4" asset catalog image resource.
    static let bg4 = ImageResource(name: "bg4", bundle: resourceBundle)

    /// The "candy" asset catalog image resource.
    static let candy = ImageResource(name: "candy", bundle: resourceBundle)

    /// The "candy 1" asset catalog image resource.
    static let candy1 = ImageResource(name: "candy 1", bundle: resourceBundle)

    /// The "candy 2" asset catalog image resource.
    static let candy2 = ImageResource(name: "candy 2", bundle: resourceBundle)

    /// The "candyf" asset catalog image resource.
    static let candyf = ImageResource(name: "candyf", bundle: resourceBundle)

    /// The "effort" asset catalog image resource.
    static let effort = ImageResource(name: "effort", bundle: resourceBundle)

    /// The "logo" asset catalog image resource.
    static let logo = ImageResource(name: "logo", bundle: resourceBundle)

}

// MARK: - Backwards Deployment Support -

/// A color resource.
struct ColorResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog color resource name.
    fileprivate let name: Swift.String

    /// An asset catalog color resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize a `ColorResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

/// An image resource.
struct ImageResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog image resource name.
    fileprivate let name: Swift.String

    /// An asset catalog image resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize an `ImageResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

#if canImport(AppKit)
@available(macOS 10.13, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

    /// Initialize a `NSColor` with a color resource.
    convenience init(resource: ColorResource) {
        self.init(named: NSColor.Name(resource.name), bundle: resource.bundle)!
    }

}

protocol _ACResourceInitProtocol {}
extension AppKit.NSImage: _ACResourceInitProtocol {}

@available(macOS 10.7, *)
@available(macCatalyst, unavailable)
extension _ACResourceInitProtocol {

    /// Initialize a `NSImage` with an image resource.
    init(resource: ImageResource) {
        self = resource.bundle.image(forResource: NSImage.Name(resource.name))! as! Self
    }

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    /// Initialize a `UIColor` with a color resource.
    convenience init(resource: ColorResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}

@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    /// Initialize a `UIImage` with an image resource.
    convenience init(resource: ImageResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Color {

    /// Initialize a `Color` with a color resource.
    init(_ resource: ColorResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}

@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Image {

    /// Initialize an `Image` with an image resource.
    init(_ resource: ImageResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}
#endif