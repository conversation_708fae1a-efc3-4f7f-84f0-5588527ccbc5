<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>colors</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameAccentColor</string>
			<key>relativePath</key>
			<string>./AccentColor.colorset</string>
			<key>swiftSymbol</key>
			<string>accent</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue</string>
			<key>relativePath</key>
			<string>./colors/blue.colorset</string>
			<key>swiftSymbol</key>
			<string>blue</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBrightgreen</string>
			<key>relativePath</key>
			<string>./colors/brightgreen.colorset</string>
			<key>swiftSymbol</key>
			<string>brightgreen</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameDarkgreen</string>
			<key>relativePath</key>
			<string>./colors/darkgreen.colorset</string>
			<key>swiftSymbol</key>
			<string>darkgreen</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameDarkred</string>
			<key>relativePath</key>
			<string>./colors/darkred.colorset</string>
			<key>swiftSymbol</key>
			<string>darkred</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGreen</string>
			<key>relativePath</key>
			<string>./colors/green.colorset</string>
			<key>swiftSymbol</key>
			<string>green</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameLightgray</string>
			<key>relativePath</key>
			<string>./colors/lightgray.colorset</string>
			<key>swiftSymbol</key>
			<string>lightgray</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameLightpink</string>
			<key>relativePath</key>
			<string>./colors/lightpink.colorset</string>
			<key>swiftSymbol</key>
			<string>lightpink</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameLightyellow</string>
			<key>relativePath</key>
			<string>./colors/lightyellow.colorset</string>
			<key>swiftSymbol</key>
			<string>lightyellow</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameOrange</string>
			<key>relativePath</key>
			<string>./colors/orange.colorset</string>
			<key>swiftSymbol</key>
			<string>orange</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNamePink</string>
			<key>relativePath</key>
			<string>./colors/pink.colorset</string>
			<key>swiftSymbol</key>
			<string>pink</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNamePurple</string>
			<key>relativePath</key>
			<string>./colors/purple.colorset</string>
			<key>swiftSymbol</key>
			<string>purple</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameRed</string>
			<key>relativePath</key>
			<string>./colors/red.colorset</string>
			<key>swiftSymbol</key>
			<string>red</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameYellow</string>
			<key>relativePath</key>
			<string>./colors/yellow.colorset</string>
			<key>swiftSymbol</key>
			<string>yellow</string>
		</dict>
	</array>
	<key>images</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdd1</string>
			<key>relativePath</key>
			<string>./icons/add 1.imageset</string>
			<key>swiftSymbol</key>
			<string>add1</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBg</string>
			<key>relativePath</key>
			<string>./images/bg.imageset</string>
			<key>swiftSymbol</key>
			<string>bg</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBg2</string>
			<key>relativePath</key>
			<string>./images/bg2.imageset</string>
			<key>swiftSymbol</key>
			<string>bg2</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBg3</string>
			<key>relativePath</key>
			<string>./images/bg3.imageset</string>
			<key>swiftSymbol</key>
			<string>bg3</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBg4</string>
			<key>relativePath</key>
			<string>./images/bg4.imageset</string>
			<key>swiftSymbol</key>
			<string>bg4</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameCandy</string>
			<key>relativePath</key>
			<string>./icons/candy.imageset</string>
			<key>swiftSymbol</key>
			<string>candy</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameCandy1</string>
			<key>relativePath</key>
			<string>./icons/candy 1.imageset</string>
			<key>swiftSymbol</key>
			<string>candy1</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameCandy2</string>
			<key>relativePath</key>
			<string>./icons/candy 2.imageset</string>
			<key>swiftSymbol</key>
			<string>candy2</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameCandyf</string>
			<key>relativePath</key>
			<string>./icons/candyf.imageset</string>
			<key>swiftSymbol</key>
			<string>candyf</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameEffort</string>
			<key>relativePath</key>
			<string>./icons/effort.imageset</string>
			<key>swiftSymbol</key>
			<string>effort</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameLogo</string>
			<key>relativePath</key>
			<string>./icons/logo.imageset</string>
			<key>swiftSymbol</key>
			<string>logo</string>
		</dict>
	</array>
	<key>symbols</key>
	<array/>
</dict>
</plist>
