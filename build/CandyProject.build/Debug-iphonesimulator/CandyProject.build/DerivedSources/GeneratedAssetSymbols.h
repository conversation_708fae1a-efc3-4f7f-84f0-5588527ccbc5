#import <Foundation/Foundation.h>

#if __has_attribute(swift_private)
#define AC_SWIFT_PRIVATE __attribute__((swift_private))
#else
#define AC_SWIFT_PRIVATE
#endif

/// The resource bundle ID.
static NSString * const ACBundleID AC_SWIFT_PRIVATE = @"com.youran.CandyProject";

/// The "AccentColor" asset catalog color resource.
static NSString * const ACColorNameAccentColor AC_SWIFT_PRIVATE = @"AccentColor";

/// The "blue" asset catalog color resource.
static NSString * const ACColorNameBlue AC_SWIFT_PRIVATE = @"blue";

/// The "brightgreen" asset catalog color resource.
static NSString * const ACColorNameBrightgreen AC_SWIFT_PRIVATE = @"brightgreen";

/// The "darkgreen" asset catalog color resource.
static NSString * const ACColorNameDarkgreen AC_SWIFT_PRIVATE = @"darkgreen";

/// The "darkred" asset catalog color resource.
static NSString * const ACColorNameDarkred AC_SWIFT_PRIVATE = @"darkred";

/// The "green" asset catalog color resource.
static NSString * const ACColorNameGreen AC_SWIFT_PRIVATE = @"green";

/// The "lightgray" asset catalog color resource.
static NSString * const ACColorNameLightgray AC_SWIFT_PRIVATE = @"lightgray";

/// The "lightpink" asset catalog color resource.
static NSString * const ACColorNameLightpink AC_SWIFT_PRIVATE = @"lightpink";

/// The "lightyellow" asset catalog color resource.
static NSString * const ACColorNameLightyellow AC_SWIFT_PRIVATE = @"lightyellow";

/// The "orange" asset catalog color resource.
static NSString * const ACColorNameOrange AC_SWIFT_PRIVATE = @"orange";

/// The "pink" asset catalog color resource.
static NSString * const ACColorNamePink AC_SWIFT_PRIVATE = @"pink";

/// The "purple" asset catalog color resource.
static NSString * const ACColorNamePurple AC_SWIFT_PRIVATE = @"purple";

/// The "red" asset catalog color resource.
static NSString * const ACColorNameRed AC_SWIFT_PRIVATE = @"red";

/// The "yellow" asset catalog color resource.
static NSString * const ACColorNameYellow AC_SWIFT_PRIVATE = @"yellow";

/// The "add 1" asset catalog image resource.
static NSString * const ACImageNameAdd1 AC_SWIFT_PRIVATE = @"add 1";

/// The "bg" asset catalog image resource.
static NSString * const ACImageNameBg AC_SWIFT_PRIVATE = @"bg";

/// The "bg2" asset catalog image resource.
static NSString * const ACImageNameBg2 AC_SWIFT_PRIVATE = @"bg2";

/// The "bg3" asset catalog image resource.
static NSString * const ACImageNameBg3 AC_SWIFT_PRIVATE = @"bg3";

/// The "bg4" asset catalog image resource.
static NSString * const ACImageNameBg4 AC_SWIFT_PRIVATE = @"bg4";

/// The "candy" asset catalog image resource.
static NSString * const ACImageNameCandy AC_SWIFT_PRIVATE = @"candy";

/// The "candy 1" asset catalog image resource.
static NSString * const ACImageNameCandy1 AC_SWIFT_PRIVATE = @"candy 1";

/// The "candy 2" asset catalog image resource.
static NSString * const ACImageNameCandy2 AC_SWIFT_PRIVATE = @"candy 2";

/// The "candyf" asset catalog image resource.
static NSString * const ACImageNameCandyf AC_SWIFT_PRIVATE = @"candyf";

/// The "effort" asset catalog image resource.
static NSString * const ACImageNameEffort AC_SWIFT_PRIVATE = @"effort";

/// The "logo" asset catalog image resource.
static NSString * const ACImageNameLogo AC_SWIFT_PRIVATE = @"logo";

#undef AC_SWIFT_PRIVATE
