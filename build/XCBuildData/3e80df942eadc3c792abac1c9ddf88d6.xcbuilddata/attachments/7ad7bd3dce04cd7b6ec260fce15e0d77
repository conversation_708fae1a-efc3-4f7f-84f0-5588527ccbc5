{"": {"diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProject-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProject-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProject-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProject-master.swiftdeps"}, "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/DerivedSources/IntentDefinitionGenerated/CandyWidget/ConfigurationIntent.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ConfigurationIntent~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/CandyProjectApp.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CandyProjectApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/AnimationSystem.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AnimationSystem~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Buttons/CloseButton.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CloseButton~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/CustomDatePicker.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CustomDatePicker~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/ModernActivityCard.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernActivityCard~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/SafeAreaHandler.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SafeAreaHandler~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/CalendarView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/CalendarView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/HomeView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/AccountView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AccountView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/DetailView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DetailView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/GoalView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GoalView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/NewActivityView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NewActivityView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/SearchView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SearchView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/TagCreationView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/TagCreationView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/Modals/UpgradeVIPView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/UpgradeVIPView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernCreateActivityView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernCreateActivityView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/ModernHomeView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ModernHomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SettingView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/SettingView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PointNotificationView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PointNotificationView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Components/Views/SubViews/PreferenceView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/PreferenceView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/DataSource.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DataSource~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/GlobalVar.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/GlobalVar~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/NotificationHandler.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/NotificationHandler~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/OpenAd.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/OpenAd~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/Persistence.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Persistence~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/ReviewHandler.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ReviewHandler~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Handlers/StoreManager.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/StoreManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/DesignSystem.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DesignSystem~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyProject/Styles/Modifiers.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Modifiers~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Activity+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Activity+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/AppSetting+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/AppSetting+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/DateValueData.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/DateValueData~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/LocalNotification+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LocalNotification+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/LongTermGoal+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/LongTermGoal+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/RemainingPoint+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/RemainingPoint+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Tag+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Tag+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/ViewType.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/ViewType~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/data/Visit+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyProject.build/Objects-normal/arm64/Visit+CoreDataProperties~partial.swiftmodule"}}