{"": {"diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidgetExtension-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidgetExtension-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidgetExtension-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidgetExtension-master.swiftdeps"}, "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/DerivedSources/IntentDefinitionGenerated/CandyWidget/ConfigurationIntent.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/ConfigurationIntent~partial.swiftmodule"}, "/Users/<USER>/Desktop/programming/CandyProject/CandyWidget/CandyWidget.swift": {"const-values": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.d", "diagnostics": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.dia", "index-unit-output-path": "/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.o", "llvm-bc": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.bc", "object": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.o", "swift-dependencies": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/programming/CandyProject/build/CandyProject.build/Debug-iphonesimulator/CandyWidgetExtension.build/Objects-normal/x86_64/CandyWidget-5da0f4283fd9f0933edc88e1efffd849~partial.swiftmodule"}}