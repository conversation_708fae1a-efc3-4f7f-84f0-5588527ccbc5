<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		bmjmp6YjKD/8eDt2UIP0izNWrsk=
		</data>
		<key>Assets.car</key>
		<data>
		BWUgAKTmaOFnk+eztBISizQGvd8=
		</data>
		<key>CandyProject.momd/CandyProject.mom</key>
		<data>
		cCWLbgpiT+sEBENv6vmo8QDIcuU=
		</data>
		<key>CandyProject.momd/VersionInfo.plist</key>
		<data>
		4PShcIg2Xmp1zKEfg6U13lVHcTM=
		</data>
		<key>CandyWidget.intentdefinition</key>
		<data>
		JxJ6YD9J29CGk6oG/juwhPOYMY0=
		</data>
		<key>Info.plist</key>
		<data>
		loTjcsDZHgbTfmWsR+v80b9xD70=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PlugIns/CandyWidgetExtension.appex/Assets.car</key>
		<data>
		G5MljoW5zfIIIdrio2d8LPJiJxI=
		</data>
		<key>PlugIns/CandyWidgetExtension.appex/CandyWidget.intentdefinition</key>
		<data>
		JxJ6YD9J29CGk6oG/juwhPOYMY0=
		</data>
		<key>PlugIns/CandyWidgetExtension.appex/CandyWidgetExtension</key>
		<data>
		OmECRZRvFTv7f/bpj6S4rZC7Wpk=
		</data>
		<key>PlugIns/CandyWidgetExtension.appex/Info.plist</key>
		<data>
		sPXzz9KVrbUpgywkjpWnNajwTIg=
		</data>
		<key>PlugIns/CandyWidgetExtension.appex/_CodeSignature/CodeResources</key>
		<data>
		BmZh90tQRb7QSOwkbyVtUqL8K4s=
		</data>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q4lWn7jx6FHwn8IIOlxe8ZOxB1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			e12XK+ClBW+w1dVVhsDGLXoh0yE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			K6DH11F6Aob666TfZ3hOTtTjM5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6Ouq21ovT9jbO90Kuk5XDQW6+BmPZT7bCHrQIQyhJ1U=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			BKwtYmqaHjqARv8tLuNr3JvWEYP1xmm29smg+lpxW9o=
			</data>
		</dict>
		<key>CandyProject.momd/CandyProject.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			H4UBq3cBvouwIobBKs4qXmqF/Snwq7mbVKj7UnchDD0=
			</data>
		</dict>
		<key>CandyProject.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tvtfzpH6aDbUXA/ms7mowjoS2kOa/Y2hbS1nyFMpqvQ=
			</data>
		</dict>
		<key>CandyWidget.intentdefinition</key>
		<dict>
			<key>hash2</key>
			<data>
			1zixPZ40vuYf+02J51XVrKy0pU/u8ydRJXku0e8cFj4=
			</data>
		</dict>
		<key>PlugIns/CandyWidgetExtension.appex/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			nwQxANMriQNzwRvsAg8brjfyMyci0VRu1nZlY7xst8Y=
			</data>
		</dict>
		<key>PlugIns/CandyWidgetExtension.appex/CandyWidget.intentdefinition</key>
		<dict>
			<key>hash2</key>
			<data>
			1zixPZ40vuYf+02J51XVrKy0pU/u8ydRJXku0e8cFj4=
			</data>
		</dict>
		<key>PlugIns/CandyWidgetExtension.appex/CandyWidgetExtension</key>
		<dict>
			<key>hash2</key>
			<data>
			Gjwr5xAKYakt1v+qIWMwzwsJjHEsWJRpVm/UuGdxfOs=
			</data>
		</dict>
		<key>PlugIns/CandyWidgetExtension.appex/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FON1FgSQxhZL35DLUTsO121hyPZTVKVvOnu8nsJ617M=
			</data>
		</dict>
		<key>PlugIns/CandyWidgetExtension.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			nLi/lKHkxbS7AD70w8uJcAZDa9vGxoEF+B5oVaMbw2U=
			</data>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SC3DHwGNr1zNCiOhi+/oYDCxHSs43KZs25/+C61MLkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			quKxHX+uTf+sOBSXHtzEOxpmH2hOmaiu4P+rai5nhEY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kUIOLBINKBUSZtAZdDcOKSQFsDfDLCY7ksD9acra2uA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
