//
//  GADNativeAdAssetIdentifiers.h
//  Google Mobile Ads SDK
//
//  Copyright 2017 Google LLC. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <GoogleMobileAds/GoogleMobileAdsDefines.h>

typedef NSString *GADNativeAssetIdentifier NS_STRING_ENUM;

extern GADNativeAssetIdentifier _Nonnull const GADNativeHeadlineAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeCallToActionAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeIconAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeBodyAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeStoreAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativePriceAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeImageAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeStarRatingAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeAdvertiserAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeMediaViewAsset;
extern GADNativeAssetIdentifier _Nonnull const GADNativeAdChoicesViewAsset;
