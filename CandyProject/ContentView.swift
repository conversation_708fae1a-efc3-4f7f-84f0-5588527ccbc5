
//  ContentView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/16.
//

import SwiftUI
import CoreData



struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @AppStorage("showCreatePainAct") var showCreatePainAct = false
    @AppStorage("actType") var actType : TagType = .pain
    @AppStorage("showActDetail") var showActDetail = false
    @AppStorage("currentActId") var currentActId : String = ""
    @State var mainViewName : ViewType = .home
    @State var showCreateActivity = false
    @ObservedObject var dataSource : ContentDataSource
    @StateObject var gVar : GlobalVar
    @ObservedObject var storeManager : StoreManager
    @StateObject private var navigationRouter = NavigationRouter()

    
    var body: some View {
        NavigationStack(path: $navigationRouter.path) {
            ZStack {

            switch mainViewName {
            case .home:
                ModernHomeView(dataSource: dataSource)
                    .background(Color.backgroundPrimary)
                    .environmentObject(navigationRouter)
            case .calendar:
                CalendarView(dataSource: dataSource)
                    .background(Color.backgroundPrimary)
            case .setting:
                SettingView(dataSource: dataSource, storeManager: storeManager, gVar : gVar)
                    .background(Color.backgroundPrimary)
            }
            
            
            HStack(spacing: 0) {
                // Calendar Tab
                Button {
                    mainViewName = .calendar
                    HapticFeedback.light()
                } label: {
                    VStack(spacing: Spacing.xs) {
                        Image(systemName: "calendar")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(mainViewName == .calendar ? .candyPink : .textSecondary)

                        Text("日历 / Calendar")
                            .font(Typography.caption)
                            .foregroundColor(mainViewName == .calendar ? .candyPink : .textSecondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.sm)
                    .background(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .fill(mainViewName == .calendar ? Color.candyPink.opacity(0.1) : Color.clear)
                    )
                }

                // Home Tab
                Button {
                    mainViewName = .home
                    HapticFeedback.light()
                } label: {
                    VStack(spacing: Spacing.xs) {
                        ZStack {
                            if mainViewName == .home {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [.candyPink, .primaryPurple],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 32, height: 32)

                                Image(systemName: "house.fill")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.white)
                            } else {
                                Image(systemName: "house")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(.textSecondary)
                            }
                        }

                        Text("首页 / Home")
                            .font(Typography.caption)
                            .foregroundColor(mainViewName == .home ? .candyPink : .textSecondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.sm)
                }

                // Profile Tab
                Button {
                    mainViewName = .setting
                    HapticFeedback.light()
                } label: {
                    VStack(spacing: Spacing.xs) {
                        Image(systemName: "person.circle")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(mainViewName == .setting ? .candyPink : .textSecondary)

                        Text("我的 / Profile")
                            .font(Typography.caption)
                            .foregroundColor(mainViewName == .setting ? .candyPink : .textSecondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.sm)
                    .background(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .fill(mainViewName == .setting ? Color.candyPink.opacity(0.1) : Color.clear)
                    )
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.sm)
            .padding(.bottom, SafeAreaHandler.getBottomSafeArea() + Spacing.md)
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.topLeft, .topRight])
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .frame(maxHeight: .infinity, alignment: .bottom)
            .ignoresSafeArea()



            if (gVar.showLoadingScreen) {
                loadingScreen
            }
            }
        }
        .navigationBarHidden(true)
        .navigationDestination(for: NavigationDestination.self) { destination in
            switch destination {
            case .createRecord:
                ModernCreateActivityView(dataSource: dataSource)
                    .environmentObject(navigationRouter)
            case .selectCandy:
                ModernNewActivityView(
                    predicate: NSPredicate(format: "type == %@", "gain"),
                    dataSource: dataSource
                )
                .environmentObject(navigationRouter)
                .onAppear {
                    actType = .gain
                }
            case .selectTask:
                ModernNewActivityView(
                    predicate: NSPredicate(format: "type == %@", "pain"),
                    dataSource: dataSource
                )
                .environmentObject(navigationRouter)
                .onAppear {
                    actType = .pain
                }
            case .createCandy:
                ModernTagCreationView(
                    dataSource: dataSource,
                    isEdit: false,
                    itemTobeEdited: nil,
                    onTagEdit: { _ in },
                    onTagAdd: { _ in }
                )
                .environmentObject(navigationRouter)
                .onAppear {
                    actType = .gain
                }
            case .createTask:
                ModernTagCreationView(
                    dataSource: dataSource,
                    isEdit: false,
                    itemTobeEdited: nil,
                    onTagEdit: { _ in },
                    onTagAdd: { _ in }
                )
                .environmentObject(navigationRouter)
                .onAppear {
                    actType = .pain
                }
            }
        }

    }
    
    var loadingScreen: some View {
        ZStack {
            Rectangle()
                .fill(Color.white)
                .ignoresSafeArea()
            
            HStack {
                Image("logo")
                    .resizable(resizingMode: .stretch)
                    .frame(width: 80, height: 80, alignment: .bottom)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                VStack {
                    Text("Candy Yourself")
                        .font(.headline)
                    Text("loading screen footnote")
                        .font(.footnote)
                }
            }
            .frame(maxHeight: .infinity, alignment: .bottomLeading)
            .padding(20)
            
        }
    }
    

}




//struct ContentView_Previews: PreviewProvider {
//    static var previews: some View {
//        ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
//    }
//}
