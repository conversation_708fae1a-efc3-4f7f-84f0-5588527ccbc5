//
//  ModernTagCreationView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/25.
//

import SwiftUI
import CoreData

struct ModernTagCreationView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var navigationManager: NavigationManager
    @AppStorage("actType") var actType: TagType = .gain
    
    @ObservedObject var dataSource: ContentDataSource
    var isEdit: Bool = false
    var itemTobeEdited: Tag?
    var onTagEdit: (_ tag: Tag?) -> Void = { _ in }
    var onTagAdd: (_ tag: Tag) -> Void = { _ in }
    
    @State private var tagName = ""
    @State private var tagPoint = 100
    @State private var showSuccessAnimation = false
    
    private var title: String {
        if isEdit {
            return actType == .gain ? "编辑糖果 / Edit Candy" : "编辑任务 / Edit Task"
        } else {
            return actType == .gain ? "创建糖果 / Create Candy" : "创建任务 / Create Task"
        }
    }
    
    private var accentColor: Color {
        actType == .gain ? .candyPink : .taskGreen
    }
    
    private var backgroundColor: [Color] {
        actType == .gain ? 
            [.candyPinkLight, .backgroundPrimary] : 
            [.taskGreenLight, .backgroundPrimary]
    }
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                colors: backgroundColor,
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Content
                ScrollView {
                    VStack(spacing: Spacing.xl) {
                        // Icon Section
                        iconSection
                        
                        // Form Section
                        formSection
                        
                        // Action Button
                        actionButtonSection
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.xl)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupInitialValues()
        }
        .onTapGesture {
            hideKeyboard()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            HStack {
                Button {
                    navigationManager.navigateBack()
                } label: {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.textPrimary)
                        .frame(width: 32, height: 32)
                        .background(Color.white)
                        .clipShape(Circle())
                }
                
                Spacer()
                
                Text(title)
                    .font(Typography.title2)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                // Placeholder for symmetry
                Color.clear
                    .frame(width: 32, height: 32)
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
            .padding(.bottom, Spacing.lg)
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.bottomLeft, .bottomRight])
        }
        .fadeIn()
    }
    
    // MARK: - Icon Section
    private var iconSection: some View {
        VStack(spacing: Spacing.md) {
            ZStack {
                Circle()
                    .fill(accentColor.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: actType == .gain ? "gift.fill" : "checkmark.circle.fill")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(accentColor)
            }
            
            Text(actType == .gain ? "创建新的糖果奖励" : "创建新的任务挑战")
                .font(Typography.bodyLarge)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .slideIn(delay: 0.1)
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: Spacing.lg) {
            // Name Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(actType == .gain ? "糖果名称 / Candy Name" : "任务名称 / Task Name")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)
                
                TextField(
                    actType == .gain ? "例如：一杯奶茶" : "例如：运动30分钟",
                    text: $tagName
                )
                .font(Typography.bodyMedium)
                .foregroundColor(.textPrimary)
                .padding(Spacing.md)
                .background(Color.backgroundSecondary)
                .cornerRadius(CornerRadius.lg)
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.lg)
                        .stroke(accentColor.opacity(0.3), lineWidth: 1)
                )
            }
            .slideIn(delay: 0.2)
            
            // Point Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(actType == .gain ? "糖果价格 / Candy Price" : "任务积分 / Task Points")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)
                
                TextField("100", value: $tagPoint, formatter: NumberFormatter())
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                    .padding(Spacing.md)
                    .background(Color.backgroundSecondary)
                    .cornerRadius(CornerRadius.lg)
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.lg)
                            .stroke(accentColor.opacity(0.3), lineWidth: 1)
                    )
            }
            .slideIn(delay: 0.3)
        }
    }
    
    // MARK: - Action Button Section
    private var actionButtonSection: some View {
        Button {
            if isEdit {
                updateTag()
            } else {
                createTag()
            }
        } label: {
            Text(isEdit ? "保存更改 / Save Changes" : (actType == .gain ? "创建糖果 / Create Candy" : "创建任务 / Create Task"))
                .font(Typography.bodyMedium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, Spacing.lg)
                .background(
                    tagName.isEmpty ?
                    LinearGradient(colors: [.gray], startPoint: .leading, endPoint: .trailing) :
                    LinearGradient(
                        colors: actType == .gain ? [.candyPink, .primaryPurple] : [.taskGreen, .primaryBlue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(CornerRadius.xl)
                .shadow(
                    color: tagName.isEmpty ? .clear : accentColor.opacity(0.3),
                    radius: 8, x: 0, y: 4
                )
        }
        .disabled(tagName.isEmpty)
        .interactiveButton()
        .scaleIn(delay: 0.4)
    }
    
    // MARK: - Helper Methods
    private func setupInitialValues() {
        if let item = itemTobeEdited {
            tagName = item.name
            tagPoint = Int(item.point)
        }
    }
    
    private func createTag() {
        guard !tagName.isEmpty else { return }
        
        let newTag = Tag(context: viewContext)
        newTag.id = UUID()
        newTag.name = tagName
        newTag.type = actType.rawValue
        newTag.point = Int16(tagPoint)
        
        dataSource.addTag(tag: newTag)
        onTagAdd(newTag)
        
        HapticFeedback.success()
        navigationManager.navigateBack()
    }
    
    private func updateTag() {
        guard !tagName.isEmpty, let item = itemTobeEdited else { return }
        
        dataSource.objectWillChange.send()
        dataSource.editTag(tagTobeEdited: item, name: tagName, point: tagPoint)
        item.name = tagName
        item.point = Int16(tagPoint)
        
        onTagEdit(item)
        HapticFeedback.success()
        navigationManager.navigateBack()
    }
    
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

#Preview {
    ModernTagCreationView(dataSource: ContentDataSource())
        .environmentObject(NavigationManager())
}
