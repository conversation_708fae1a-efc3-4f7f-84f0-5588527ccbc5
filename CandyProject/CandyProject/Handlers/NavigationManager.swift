//
//  NavigationManager.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/25.
//

import SwiftUI
import Foundation

// MARK: - Navigation Routes
enum NavigationRoute: Hashable {
    case home
    case calendar
    case setting
    case createActivity
    case createCandy
    case createTask
    case editCandy(Tag)
    case editTask(Tag)
}

// MARK: - Navigation Manager
class NavigationManager: ObservableObject {
    @Published var path = NavigationPath()
    @Published var currentTab: ViewType = .home
    
    func navigate(to route: NavigationRoute) {
        path.append(route)
    }
    
    func navigateBack() {
        if !path.isEmpty {
            path.removeLast()
        }
    }
    
    func navigateToRoot() {
        path = NavigationPath()
    }
    
    func switchTab(to tab: ViewType) {
        currentTab = tab
        navigateToRoot() // Clear navigation stack when switching tabs
    }
}

// MARK: - Navigation Extensions
extension NavigationManager {
    func navigateToCreateActivity() {
        navigate(to: .createActivity)
    }
    
    func navigateToCreateCandy() {
        navigate(to: .createCandy)
    }
    
    func navigateToCreateTask() {
        navigate(to: .createTask)
    }
}
