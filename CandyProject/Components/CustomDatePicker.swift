//
//  CustomDatePicker.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/5/3.
//

import SwiftUI

struct CustomDatePicker: View {
    @Binding var selectedDate: Date
    @State var currentMonth: Int = 0
    @State var dataSource: ContentDataSource
    @State var dataMap: [Date : [Bool]] = [:]
    @State var monthlyActs: [Activity] = []
    let currentDate: Date = Date()
    @AppStorage("startWithMon") var startWithMon = false
    let calendar = Calendar.current
    
    var body: some View {
        VStack(spacing: 0){
            HStack{
                VStack(alignment: .leading, spacing: 10) {
                    //yyyy
                    Text(extraData()[0])
                        .font(.caption)
                        .fontWeight(.semibold)
                    //mmmm
                    Text(extraData()[1])
                        .font(.title2.bold())
                }
                
                Spacer(minLength: 0)
                
                Button {
                    currentMonth -= 1
                } label: {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                }
                
                
                Button {
                    currentMonth = 0
                } label: {
                    Text("Today")
                }
                
                But<PERSON> {
                    currentMonth += 1
                } label: {
                    Image(systemName: "chevron.right")
                        .font(.title2)
                }
                
            }
            .padding(.horizontal, 10)
            
            HStack(spacing: 0) {
                let days = startWithMon ? ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"] : ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
                ForEach(days, id: \.self) {day in
                    Text(LocalizedStringKey(day))
                        .font(.callout)
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                }
            }
            
            let columns = Array(repeating: GridItem(.flexible(), spacing: 0), count: 7)
            
            LazyVGrid(columns: columns, spacing: 0) {
                ForEach(extractDate()) { value in
                    GeometryReader { gr in
                        CardView(value: value, sideLength: gr.size.width)
                            .onTapGesture {
                                selectedDate = value.date
                                let thecurrentmonth = getCurrentMonth()
                                let alldays = thecurrentmonth.getAllDates()
                                monthlyActs = dataSource.getActivitiesInRange(date1: alldays.first!, date2: alldays.last!)
                                dataSource.loadActivitiesByDate(date: value.date)
                            }
                            .padding(0)
                            .frame(height: gr.size.width)
                    }
                    .aspectRatio(1, contentMode: .fit)
                    
                }
            }.padding(0)
        }
        .onChange(of: currentMonth) { newvalue in
            selectedDate = getCurrentMonth()
            let thecurrentmonth = getCurrentMonth()
            let alldays = thecurrentmonth.getAllDates()
            monthlyActs = dataSource.getActivitiesInRange(date1: alldays.first!, date2: alldays.last!)
            dataSource.loadActivitiesByDate(date: selectedDate)
        }
        .onAppear {
            let currentmonth = getCurrentMonth()
            let alldays = currentmonth.getAllDates()
            monthlyActs = dataSource.getActivitiesInRange(date1: alldays.first!, date2: alldays.last!)
        }
        .gesture(DragGesture(minimumDistance: 0, coordinateSpace: .local)
            .onEnded({ value in
                if value.translation.width < 0 {
                    currentMonth += 1
                }
                
                if value.translation.width > 0 {
                    currentMonth -= 1
                }
            }))
    }
    
    
    @ViewBuilder
    func CardView(value: DateValueData, sideLength: CGFloat) -> some View {
        VStack(spacing: 0){
            if (value.day != -1) {
                
                if(value.data[2] != 0) {
                    Text("+\(value.data[2]+value.data[0])")
                        .font(.custom("", fixedSize: sideLength / 5))
                        .foregroundColor(Color("darkred"))
                        .frame(height: sideLength / 5, alignment: .top)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                } else if (value.data[0] != 0) {
                    Text("+\(value.data[0])")
                        .font(.custom("", fixedSize: sideLength / 5))
                        .foregroundColor(Color("red"))
                        .frame(height: sideLength / 5, alignment: .top)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                } else {
                    Text("")
                        .font(.custom("", fixedSize: sideLength / 5))
                        .foregroundColor(Color("red"))
                        .frame(height: sideLength / 5, alignment: .top)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                    
                }
                Text("\(value.day)")
                    .font(.custom("", fixedSize: 20))
                    .foregroundColor(isSameDay(date1: currentDate, date2: value.date) ? .blue : .black)
                    .frame(height: 3 * sideLength / 5, alignment: .center)
                    .frame(maxWidth: .infinity, alignment: .center)
                if(value.data[1] != 0) {
                    Text("-\(value.data[1])")
                        .font(.custom("", fixedSize: sideLength / 5))
                        .foregroundColor(Color("darkgreen"))
                        .frame(height: sideLength  / 5, alignment: .bottom)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                } else {
                    Text("")
                        .font(.custom("", fixedSize: sideLength / 5))
                        .foregroundColor(Color("darkgreen"))
                        .frame(height: sideLength / 5, alignment: .top)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                }
                
            }
        }
        .frame(height: sideLength)
        .modifier(RoundedRectOutlinedBgModifier(fillcolor: Color("lightyellow"), op: isSameDay(date1: selectedDate, date2: value.date) ? 0.3 : 0, strokeWidth: isSameDay(date1: selectedDate, date2: value.date) ? 0 : 1))
    }
    
    func isSameDay(date1: Date, date2: Date) -> Bool {
        return calendar.isDate(date1, inSameDayAs: date2)
    }
    
    func extraData() -> [String] {
        let formatter = DateFormatter()
        formatter.dateFormat = "YYYY MMMM"
        
        let date = formatter.string(from: selectedDate)
        return date.components(separatedBy: " ")
    }
    
    func getCurrentMonth() -> Date {
        guard let thecurrentmonth = calendar.date(byAdding: .month, value: self.currentMonth, to: Date()) else {
            return Date()
        }
        return thecurrentmonth
    }
    
    func extractDate() -> [DateValueData] {
        // get current month date
        let currentmonth = getCurrentMonth()
        let alldays = currentmonth.getAllDates()
        var days = alldays.compactMap {date -> DateValueData in
            // get day
            let day = calendar.component(.day, from: date)
            var painP = 0
            var gainP = 0
            var goalP = 0
            for act in monthlyActs {
                if (isSameDay(date1: act.createdAt, date2: date)) {
                    if (act.type == "goal") {
                        goalP += Int(act.point)
                    } else {
                        if (act.tagType == "pain") {
                            painP += Int(act.point)
                        }
                        if (act.tagType == "gain") {
                            gainP += Int(act.point)
                        }
                    }
                }
            }
            let data = [painP, gainP, goalP]
            return DateValueData(day: day, date: date, data: data)
        }
        
        let firstWeekDay = calendar.component(.weekday, from: days.first?.date ?? Date())
        
        // get offsets
        if (startWithMon) {
            if (firstWeekDay - 2 < 0) {
                for _ in 0..<6 {
                    days.insert(DateValueData(day: -1, date: Date(), data: [0, 0, 0]), at: 0)
                }
            } else {
                for _ in 0..<firstWeekDay - 2 {
                    days.insert(DateValueData(day: -1, date: Date(), data: [0, 0, 0]), at: 0)
                }
                
            }
        } else {
            for _ in 0..<firstWeekDay - 1 {
                days.insert(DateValueData(day: -1, date: Date(), data: [0, 0, 0]), at: 0)
            }
        }
        
        
        return days
    }
}

extension Date {
    func getAllDates() -> [Date] {
        let calendar = Calendar.current
        // get start date
        let startdate = calendar.date(from: calendar.dateComponents([.year, .month], from: self))!
        
        let range = calendar.range(of: .day, in: .month, for: self)!
        
        return range.compactMap { day -> Date in
            return calendar.date(byAdding: .day, value: day - 1, to: startdate)!
        }
    }
}
