//
//  AnimationSystem.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI

// MARK: - Animation Presets
struct AnimationPresets {
    static let spring = Animation.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)
    static let easeInOut = Animation.easeInOut(duration: 0.3)
    static let easeOut = Animation.easeOut(duration: 0.2)
    static let bouncy = Animation.spring(response: 0.4, dampingFraction: 0.6, blendDuration: 0)
    static let smooth = Animation.timingCurve(0.2, 0.8, 0.2, 1, duration: 0.4)
    static let quick = Animation.easeInOut(duration: 0.15)
}

// MARK: - Animated Card Modifier
struct AnimatedCardModifier: ViewModifier {
    @State private var isPressed = false
    @State private var isVisible = false
    
    let delay: Double
    
    init(delay: Double = 0) {
        self.delay = delay
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .opacity(isVisible ? 1.0 : 0.0)
            .offset(y: isVisible ? 0 : 20)
            .animation(AnimationPresets.spring, value: isPressed)
            .animation(AnimationPresets.smooth.delay(delay), value: isVisible)
            .onAppear {
                withAnimation {
                    isVisible = true
                }
            }
            .onTapGesture {
                withAnimation(AnimationPresets.quick) {
                    isPressed = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(AnimationPresets.quick) {
                        isPressed = false
                    }
                }
            }
    }
}

// MARK: - Slide In Animation
struct SlideInModifier: ViewModifier {
    @State private var isVisible = false
    let direction: SlideDirection
    let delay: Double
    
    enum SlideDirection {
        case left, right, top, bottom
    }
    
    init(from direction: SlideDirection = .bottom, delay: Double = 0) {
        self.direction = direction
        self.delay = delay
    }
    
    private var offset: CGSize {
        if isVisible {
            return .zero
        }
        
        switch direction {
        case .left:
            return CGSize(width: -100, height: 0)
        case .right:
            return CGSize(width: 100, height: 0)
        case .top:
            return CGSize(width: 0, height: -100)
        case .bottom:
            return CGSize(width: 0, height: 100)
        }
    }
    
    func body(content: Content) -> some View {
        content
            .offset(offset)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(AnimationPresets.smooth.delay(delay), value: isVisible)
            .onAppear {
                withAnimation {
                    isVisible = true
                }
            }
    }
}

// MARK: - Fade In Animation
struct FadeInModifier: ViewModifier {
    @State private var isVisible = false
    let delay: Double
    
    init(delay: Double = 0) {
        self.delay = delay
    }
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(AnimationPresets.easeInOut.delay(delay), value: isVisible)
            .onAppear {
                withAnimation {
                    isVisible = true
                }
            }
    }
}

// MARK: - Scale In Animation
struct ScaleInModifier: ViewModifier {
    @State private var isVisible = false
    let delay: Double
    
    init(delay: Double = 0) {
        self.delay = delay
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(AnimationPresets.bouncy.delay(delay), value: isVisible)
            .onAppear {
                withAnimation {
                    isVisible = true
                }
            }
    }
}

// MARK: - Pulse Animation
struct PulseModifier: ViewModifier {
    @State private var isPulsing = false
    let duration: Double
    
    init(duration: Double = 1.0) {
        self.duration = duration
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing ? 1.05 : 1.0)
            .animation(
                Animation.easeInOut(duration: duration).repeatForever(autoreverses: true),
                value: isPulsing
            )
            .onAppear {
                isPulsing = true
            }
    }
}

// MARK: - Shimmer Effect
struct ShimmerModifier: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                .clear,
                                .white.opacity(0.3),
                                .clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .animation(
                        Animation.linear(duration: 1.5).repeatForever(autoreverses: false),
                        value: phase
                    )
            )
            .onAppear {
                phase = 300
            }
    }
}

// MARK: - Haptic Feedback
struct HapticFeedback {
    static func light() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    static func medium() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    static func heavy() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    static func success() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    static func error() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    static func warning() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)
    }
}

// MARK: - Interactive Button Style
struct InteractiveButtonStyle: ButtonStyle {
    let hapticFeedback: Bool
    
    init(hapticFeedback: Bool = true) {
        self.hapticFeedback = hapticFeedback
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(AnimationPresets.quick, value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { isPressed in
                if isPressed && hapticFeedback {
                    HapticFeedback.light()
                }
            }
    }
}

// MARK: - View Extensions
extension View {
    func animatedCard(delay: Double = 0) -> some View {
        modifier(AnimatedCardModifier(delay: delay))
    }
    
    func slideIn(from direction: SlideInModifier.SlideDirection = .bottom, delay: Double = 0) -> some View {
        modifier(SlideInModifier(from: direction, delay: delay))
    }
    
    func fadeIn(delay: Double = 0) -> some View {
        modifier(FadeInModifier(delay: delay))
    }
    
    func scaleIn(delay: Double = 0) -> some View {
        modifier(ScaleInModifier(delay: delay))
    }
    
    func pulse(duration: Double = 1.0) -> some View {
        modifier(PulseModifier(duration: duration))
    }
    
    func shimmer() -> some View {
        modifier(ShimmerModifier())
    }
    
    func interactiveButton(hapticFeedback: Bool = true) -> some View {
        buttonStyle(InteractiveButtonStyle(hapticFeedback: hapticFeedback))
    }
}
