//
//  ModernTagCreationView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import CoreData

struct ModernTagCreationView: View {
    @EnvironmentObject private var navigationRouter: NavigationRouter
    @AppStorage("actType") var actType: TagType = .gain
    @State private var tagname: String = ""
    @State private var tagpoint: Int = 10
    
    let dataSource: ContentDataSource
    let isEdit: Bool
    let itemTobeEdited: Tag?
    let onTagEdit: (Tag?) -> Void
    let onTagAdd: (Tag) -> Void
    
    private var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    
    private var title: String {
        isEdit ? "编辑\(actType == .gain ? "糖果" : "任务")" : "创建\(actType == .gain ? "糖果" : "任务")"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: actType == .gain ?
                        [.candyPinkLight, .backgroundPrimary] :
                        [.taskGreenLight, .backgroundPrimary],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerSection
                    
                    // Content
                    ScrollView {
                        VStack(spacing: Spacing.xl) {
                            // Form
                            formSection
                            
                            Spacer(minLength: 100)
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.top, Spacing.lg)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            if isEdit, let item = itemTobeEdited {
                tagname = item.name
                tagpoint = Int(item.point)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            HStack {
                Button {
                    navigationRouter.goBack()
                } label: {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(Color.white)
                        .clipShape(Circle())
                }

                Spacer()

                Text(title)
                    .font(Typography.title2)
                    .foregroundColor(.textPrimary)

                Spacer()

                Button {
                    navigationRouter.goBack()
                } label: {
                    Image(systemName: "xmark")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(Color.white)
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
            .padding(.bottom, Spacing.lg)
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.bottomLeft, .bottomRight])
        }
        .fadeIn()
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: Spacing.lg) {
            // Name Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(actType == .gain ? "糖果名称 / Candy Name" : "任务名称 / Task Name")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)

                TextField(
                    actType == .gain ? "例如：一杯奶茶" : "例如：运动30分钟",
                    text: $tagname
                )
                .font(Typography.bodyMedium)
                .foregroundColor(.textPrimary)
                .padding(Spacing.md)
                .background(Color.backgroundSecondary)
                .cornerRadius(CornerRadius.lg)
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.lg)
                        .stroke((actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3), lineWidth: 1)
                )
            }
            
            // Points Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("积分 / Points")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)
                
                HStack {
                    Button {
                        if tagpoint > 1 {
                            tagpoint -= 1
                        }
                    } label: {
                        Image(systemName: "minus")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(actType == .gain ? .candyPink : .taskGreen)
                            .frame(width: 32, height: 32)
                            .background(Color.backgroundSecondary)
                            .clipShape(Circle())
                    }
                    
                    Spacer()
                    
                    Text("\(tagpoint)")
                        .font(Typography.title2)
                        .foregroundColor(.textPrimary)
                    
                    Spacer()
                    
                    Button {
                        if tagpoint < 100 {
                            tagpoint += 1
                        }
                    } label: {
                        Image(systemName: "plus")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(actType == .gain ? .candyPink : .taskGreen)
                            .frame(width: 32, height: 32)
                            .background(Color.backgroundSecondary)
                            .clipShape(Circle())
                    }
                }
                .padding(Spacing.md)
                .background(Color.backgroundSecondary)
                .cornerRadius(CornerRadius.lg)
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.lg)
                        .stroke((actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3), lineWidth: 1)
                )
            }
            
            // Action Button
            Button {
                saveTag()
            } label: {
                Text(isEdit ? "保存更改 / Save Changes" : (actType == .gain ? "创建糖果 / Create Candy" : "创建任务 / Create Task"))
                    .font(Typography.bodyMedium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.lg)
                    .background(
                        tagname.isEmpty ?
                        LinearGradient(colors: [.gray], startPoint: .leading, endPoint: .trailing) :
                        LinearGradient(
                            colors: actType == .gain ? [.candyPink, .primaryPurple] : [.taskGreen, .primaryBlue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(CornerRadius.xl)
                    .shadow(
                        color: tagname.isEmpty ? .clear : (actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3),
                        radius: 8, x: 0, y: 4
                    )
            }
            .disabled(tagname.isEmpty)
        }
    }
    
    // MARK: - Actions
    private func saveTag() {
        guard !tagname.isEmpty else { return }

        if let itemTobeEdited = itemTobeEdited {
            dataSource.objectWillChange.send()
            dataSource.editTag(tagTobeEdited: itemTobeEdited, name: tagname, point: tagpoint)
            itemTobeEdited.name = tagname
            itemTobeEdited.point = Int16(tagpoint)
            onTagEdit(itemTobeEdited)
            HapticFeedback.success()
            navigationRouter.goBack()
        } else {
            let newTag = Tag(context: viewContext)
            newTag.id = UUID()
            newTag.name = tagname
            newTag.type = actType.rawValue
            newTag.point = Int16(tagpoint)
            dataSource.addTag(tag: newTag)
            onTagAdd(newTag)
            HapticFeedback.success()
            navigationRouter.goBack()
        }
    }
}
