//
//  HomeView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/17.
//

import CoreData
import SwiftUI

struct HomeView: View {
    
    @AppStorage("showCreateGainAct") var showCreateGainAct = false
    @AppStorage("actType") var actType : TagType = .pain
    @AppStorage("showGoalView") var showGoalView = false
    @State var goalDisabled = false
    @State var selectedDate : Date = Date()
    @State var selectedAct : Activity?
    @State var progressForGoal : Double = 0
    @State var goalExpanded: Bool = true
    var dataSource : ContentDataSource
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    
    @State var longTermGoals: [LongTermGoal] = []
    
    init(dataSource: ContentDataSource){
        UITableView.appearance().backgroundColor = .clear
        UITableViewCell.appearance().backgroundColor = .clear
        self.dataSource = dataSource
    }
    
    var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }
    
    func groupActByDate(_ result : [Activity])-> [[Activity]]{
        let groupedAct = Dictionary(grouping: result){ (element : Activity)  in
            dateFormatter.string(from: element.createdAt)
        }
        var sortedGroupedAct: [[Activity]] = []
        groupedAct.sorted{$0.key > $1.key}.forEach {entry in
            let (_, actItems) = entry
            sortedGroupedAct.append(actItems)
            
            //            actItems.forEach {actItem in
            //                print("\(actItem.id) : \(actItem.tagName) : \(actItem.createdAt.formatted())")
            //            }
        }
        return sortedGroupedAct
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 24) {
                // Balance Card
                VStack(spacing: 24) {
                    // Balance Display
                    VStack(spacing: 8) {
                        Text("当前积分")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(dataSource.balance)")
                            .font(.system(size: 48, weight: .bold, design: .rounded))
                            .foregroundColor(.blue)
                    }

                    // Candy Button
                    NavigationLink(destination: NewActivityView(predicate: NSPredicate(value: true), dataSource: dataSource)
                        .onAppear { actType = .gain }) {
                        HStack(spacing: 8) {
                            Image(systemName: "gift.fill")
                                .font(.system(size: 20, weight: .medium))

                            Text("兑换糖果")
                                .font(.body.weight(.medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 32)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: [.pink, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(20)
                        .shadow(color: .pink.opacity(0.3), radius: 8, x: 0, y: 4)
                    }
                }
                .padding(32)
                .background(
                    LinearGradient(
                        colors: [.white, Color(.secondarySystemBackground)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .cornerRadius(20)
                .shadow(color: .black.opacity(0.1), radius: 16, x: 0, y: 8)
                .padding(.horizontal, 16)
                .padding(.top, 16)

                // Goals Section
                if !dataSource.goals.isEmpty {
                    goalsSection
                }

                // Activities Section
                if dataSource.activities.isEmpty && dataSource.goals.isEmpty {
                    emptyStateView
                } else {
                    activitiesSection
                }

                // Bottom spacing for tab bar
                Color.clear.frame(height: 100)
            }
        }
        .background(Color.backgroundPrimary)
        .onAppear() {
            dataSource.updateBalance()
            dataSource.loadMoreContent()
            dataSource.loadTagList(predicate: NSPredicate(value: true))
            dataSource.loadGoals()
            longTermGoals = dataSource.goals
        }
        
    }
    

    
    var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "list.bullet.clipboard")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(.gray)

            VStack(spacing: 8) {
                Text("还没有活动记录")
                    .font(.title2)
                    .foregroundColor(.primary)

                Text("点击下方的 + 按钮开始记录任务")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                Text("点击上方的糖果按钮兑换奖励")
                    .font(.footnote)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(20)
        .padding(.horizontal, 16)
    }
    
    var goalsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("目标进度")
                    .font(.title3)
                    .foregroundColor(.primary)

                Spacer()

                Button {
                    dataSource.loadTagList(predicate: NSPredicate(value: true))
                    goalDisabled = dataSource.getTagListByType(type: "pain").isEmpty
                    guard !goalDisabled else {
                        return
                    }
                    showGoalView = true
                } label: {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)

            LazyVStack(spacing: 8) {
                ForEach(longTermGoals, id: \.self) { goal in
                    goalCard(goal: goal)
                        .padding(.horizontal, 16)
                }
                .onDelete(perform: { offsets in
                    for index in offsets {
                        dataSource.deleteGoal(goal: dataSource.goals[index])
                        longTermGoals = dataSource.goals
                    }
                })
            }
        }
        .alert("要创建目标，需要先创建任务", isPresented: $goalDisabled) {
            Button("确定", role: .cancel) { }
        }
        .fullScreenCover(isPresented: $showGoalView, content: {
            GoalView(dataSource: dataSource, predicate: NSPredicate(format: "type == %@", "pain"), onCreate: {
                longTermGoals = dataSource.goals
            })
        })
    }

    func goalCard(goal: LongTermGoal) -> some View {
        let progress = Double(goal.currentNo) / Double(goal.targetNo)
        let progressColor: Color = {
            if progress >= 1.0 {
                return .green
            } else if progress >= 0.7 {
                return .orange
            } else {
                return .blue
            }
        }()

        return VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(goal.targetNo) x \(dataSource.getTagNameById(goal.tagId))")
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)

                    Text("奖励: +\(goal.point) 积分")
                        .font(.footnote)
                        .foregroundColor(.green)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(goal.currentNo)/\(goal.targetNo)")
                        .font(.headline)
                        .foregroundColor(progressColor)

                    Text(formatDateRange(start: goal.startTime, end: goal.endTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Progress Bar
            VStack(alignment: .leading, spacing: 4) {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
                    .scaleEffect(x: 1, y: 2, anchor: .center)

                HStack {
                    Text("进度")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("\(Int(progress * 100))%")
                        .font(.caption)
                        .foregroundColor(progressColor)
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(progressColor.opacity(0.2), lineWidth: 1)
        )
    }

    private func formatDateRange(start: Date, end: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return "\(formatter.string(from: start)) - \(formatter.string(from: end))"
    }

    var activitiesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("最近活动")
                    .font(.title3)
                    .foregroundColor(.primary)

                Spacer()
            }
            .padding(.horizontal, 16)

            LazyVStack(spacing: 8) {
                ForEach(groupActByDate(dataSource.getActivities()), id: \.self) { section in
                    activitySection(
                        title: formatSectionDate(section[0].createdAt),
                        activities: section
                    )
                }
            }
        }
        .fullScreenCover(item: $selectedAct, content: { act in
            DetailView(currentAct: act, dataSource: dataSource)
        })
    }

    func activitySection(title: String, activities: [Activity]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            HStack {
                Text(title)
                    .font(.title3)
                    .foregroundColor(.primary)

                Spacer()

                Text("\(activities.count)项")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(8)
            }
            .padding(.horizontal, 16)

            // Activities
            LazyVStack(spacing: 8) {
                ForEach(activities, id: \.id) { activity in
                    activityCard(activity: activity)
                        .padding(.horizontal, 16)
                }
                .onDelete(perform: { offsets in
                    for index in offsets {
                        dataSource.revertActivity(act: activities[index])
                        dataSource.deleteActivity(act: activities[index])
                    }
                })
            }
        }
    }

    func activityCard(activity: Activity) -> some View {
        let tagType = TagType(rawValue: activity.tagType) ?? .pain
        let backgroundColor: Color = tagType == .pain ? .green.opacity(0.1) : .pink.opacity(0.1)
        let accentColor: Color = tagType == .pain ? .green : .pink
        let iconName = tagType == .pain ? "checkmark.circle.fill" : "gift.fill"
        let pointPrefix = tagType == .pain ? "+" : "-"

        return Button {
            selectedAct = activity
        } label: {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: iconName)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(accentColor)
                    .frame(width: 32, height: 32)

                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(activity.tagName)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    if let note = activity.note, !note.isEmpty {
                        Text(note)
                            .font(.footnote)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }

                    Text(formatDate(activity.createdAt))
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Points
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(pointPrefix)\(activity.point)")
                        .font(.headline)
                        .foregroundColor(accentColor)

                    Text("积分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(16)
            .background(backgroundColor)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(accentColor.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func formatSectionDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: date)
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd HH:mm"
        return formatter.string(from: date)
    }
    

    
}
