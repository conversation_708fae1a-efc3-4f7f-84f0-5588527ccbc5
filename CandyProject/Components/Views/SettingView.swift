//
//  SettingView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/18.
//

import SwiftUI

struct SettingView: View {
    @AppStorage("showUpgradeVIP") var showUpgradeVIP = false
    var dataSource : ContentDataSource
    var storeManager : StoreManager
    var gVar : GlobalVar
    
    init(dataSource : ContentDataSource, storeManager : StoreManager, gVar : GlobalVar){
        UITableView.appearance().backgroundColor = .clear
        UITableViewCell.appearance().backgroundColor = .clear
        self.dataSource = dataSource
        self.storeManager = storeManager
        self.gVar = gVar
    }
    
    private let quantityFormatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.zeroSymbol = ""
        return formatter
    }()
    
    
    
    var body: some View {
        
        
        NavigationView {
            ZStack{
                Image("bg2")
                    .resizable(resizingMode: .stretch)
                    .ignoresSafeArea()
                
                List {
                    Section {
                        NavigationLink(destination: PointNotificationView(dataSource: dataSource)){
                            Label("DAILY NOTIFICATION FOR LOW POINT", systemImage: "captions.bubble")
                        }
                        
                        if (!storeManager.myProducts.isEmpty) {
                            Button {
                                showUpgradeVIP = true
                            } label: {
                                Label("Upgrade to VIP", systemImage: "crown")
                            }
                            .sheet(isPresented: $showUpgradeVIP, content: {
                                UpgradeVIPView(dataSource: dataSource, storeManager: storeManager, gVar: gVar)
                            })
                        }
                        
                        NavigationLink(destination: PreferenceView()){
                            Label("Preferences", systemImage: "gear")
                        }
                        
                        Button {
                            guard let url = URL(string: "itms-apps://itunes.apple.com/app/id1617783916?mt=8&action=write-review")
                            else {
                                return
                            }
                            UIApplication.shared.open(url, options: [:], completionHandler: nil)
                        } label: {
                            Label("Rate Us", systemImage: "star")
                        }
                        
                        Label("icloud synced", systemImage: "icloud")
                        
                    }
                    .listRowSeparator(.automatic)
                    .padding(.top, 20)
                    .listRowBackground(
                        Color.clear.blendMode(.softLight)
                    )
                }
                .listStyle(.insetGrouped)
                
                
            }.navigationTitle("Settings")
        }
        
        
        
    }
    
}


//struct SettingView_Previews: PreviewProvider {
//    static var previews: some View {
//        SettingView()
//    }
//}

