//
//  CalendarView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/30.
//

import SwiftUI

struct CalendarView: View {
    @State var selectedDate = Date()
    @State var selectedAct : Activity?
    @AppStorage("showActDetail") var showActDetail = false
    var dataSource : ContentDataSource
    
    init(dataSource: ContentDataSource){
        self.dataSource = dataSource
        UITableView.appearance().backgroundColor = .clear
        UITableViewCell.appearance().backgroundColor = .clear
    }
    
    var body: some View {
        ZStack {
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
            
            VStack {

                CustomDatePicker(selectedDate: $selectedDate, dataSource: dataSource)
                    .frame(maxHeight: .infinity, alignment: .top)
                    .onAppear(
                        perform: {
                            dataSource.loadActivitiesByDate(date: selectedDate)
                        }
                    )

                List {
                    ForEach(dataSource.activitiesByDate) { act in
                        if (!act.isFault) {
                            But<PERSON> {
                                showActDetail = true
                                selectedAct = act
                            } label: {
                                GeometryReader { metrics in
                                    HStack {
                                        Text("\(act.tagName)")
                                            .foregroundColor(Color.black)
                                            .padding(10)
                                            .modifier(dataSource.getListModifier(act: act, width: metrics.size.width * 0.8))
                                        
                                        Text("\(act.tagType == "pain" ? "+" : "-") \(act.point)")
                                            .foregroundColor(Color.black)
                                            .modifier(dataSource.getListModifier(act: act, width: metrics.size.width * 0.2))
                                        
                                    }
                                    .frame(maxHeight: .infinity, alignment: .center)
                                }
                            }
                            .fullScreenCover(item: $selectedAct, content: { act in
                                DetailView(currentAct: act, dataSource: dataSource, callback: activityEditCB)
                            })
                        }
                        
                    }
                    .listRowBackground(
                        Color.gray.opacity(0.1)
                    )
                }
                .listRowSeparator(.hidden)
                
            }
            .padding(.top, 60)
        }

    }
    
    func activityEditCB() {
        dataSource.loadActivitiesByDate(date: selectedDate)
    }
}

//struct CalendarView_Previews: PreviewProvider {
//    static var previews: some View {
//        CalendarView()
//    }
//}
