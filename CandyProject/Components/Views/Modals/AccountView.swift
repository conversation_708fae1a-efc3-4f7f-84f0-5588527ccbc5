//
//  AccountView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/17.
//

import SwiftUI
import AuthenticationServices

struct AccountView: View {
    
    @AppStorage("showAccountView") var showAccountView = true
    @State var showAppleSignIn = true
    @State var givenName = ""
    @State var familyName = ""
    @State var userEmail = ""
    @State var userID = ""
    
    
    var body: some View {
        ZStack {
            Rectangle()
                .fill(.ultraThinMaterial)
                .ignoresSafeArea()
            
            CloseButton()
            
            VStack {
                if (showAppleSignIn) {
                    SignInWithAppleButton(.signIn,
                                          onRequest: { request in
                        request.requestedScopes = [.fullName, .email]
                    },
                                          onCompletion: { result in
                        didFinishAuthentication(result: result)
                    }
                    )
                    .frame(width: 200, height: 60)
                }
                
                if (!showAppleSignIn) {
                    Text("userID:" + userID)
                    Text("name:" + familyName + " " + givenName)
                    Text("E-mail:" + userEmail)
                }
            }
        }
    }
    
    func didFinishAuthentication(result: Result<ASAuthorization, Error>) {
        switch result {
        case .success(let authorization):
            guard let credential = authorization.credential as? ASAuthorizationAppleIDCredential else {
                print("some errors, try again")
                return
            }
            print("given name:", credential.fullName?.givenName)
            print("family name:", credential.fullName?.familyName)
            print("email:", credential.email)
            print("userID:", credential.user)
            
            userID = credential.user
            givenName = credential.fullName?.givenName ?? ""
            familyName = credential.fullName?.familyName ?? ""
            userEmail = credential.email ?? ""
            showAppleSignIn = false
            
            // TODO: save use data
            
        case .failure(let error):
            print("error on apple sign in:", error)
        }
    }
}

struct AccountView_Previews: PreviewProvider {
    static var previews: some View {
        AccountView()
    }
}
