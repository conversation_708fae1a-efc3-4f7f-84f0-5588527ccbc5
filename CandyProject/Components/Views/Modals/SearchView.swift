//
//  SearchView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/1.
//

import SwiftUI

struct SearchView: View {
    @AppStorage("showSearchView") var showSearchView = true
    @State var text = ""
    @State var dataSource : ContentDataSource
    
    var body: some View {
        ZStack {
            Rectangle()
                .fill(.ultraThinMaterial)
                .ignoresSafeArea()
            
            CloseButton()
            
            NavigationView {
                VStack {
//                    content
                    Spacer()
                }
            }
            
            .searchable(text: $text) {
//                dataSource.loadActivitiesBySearch(text: text)
//                ForEach(suggestions) { suggestion in
//                    Button {
//                        text = suggestion.text
//                    } label: {
//                        Text(suggestion.text)
//                    }
//                    .searchCompletion(suggestion.text)
//                }
            }
            .padding(.top, 60)
            
        }
    }
}

//struct SearchView_Previews: PreviewProvider {
//    static var previews: some View {
//        SearchView()
//    }
//}
