//
//  DetailView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/21.
//

import SwiftUI

struct DetailView: View {
    @AppStorage("showActDetail") var showActDetail = true
    @AppStorage("currentActId") var currentActId : String = ""
    @ObservedObject var currentAct: Activity
    @State var currentActNote: String = ""
    @State var noteEditable: Bool = false
    @State var originalActDate: Date = Date()
    @Environment(\.managedObjectContext) private var viewContext
    var dataSource : ContentDataSource
    
    var callback: () -> Void = {}
    
    var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "E, dd MMM yyyy HH:mm:ss"
        return formatter
    }
    
    var body: some View {
        let name = "\(currentAct.tagName )"
        let createdAt = "\(dateFormatter.string(from: currentAct.createdAt ))"
        let point = String(currentAct.point)


        ZStack {

            if (currentAct.tagType == "pain") {
                Image("bg3")
                    .resizable(resizingMode: .stretch)
                    .ignoresSafeArea()
            }

            if (currentAct.tagType == "gain") {
                Image("bg4")
                    .resizable(resizingMode: .stretch)
                    .ignoresSafeArea()
            }

            
            CloseButton(callback: callback)

            VStack(alignment: .leading, spacing: 20) {
                currentAct.newTagType == .gain ?
                Text("You have redeemed \(name) at \(createdAt) and it costed \(point) points")
                    .font(.headline) :
                Text("You have done \(name) at \(createdAt) and earned \(point) points")
                    .font(.headline)
                
                if (currentAct.newType == .goal) {
                    Text("\(dateFormatter.string(from: dataSource.getGoalById(currentAct.goalId!)!.startTime)) - \(dateFormatter.string(from:dataSource.getGoalById(currentAct.goalId!)!.endTime))")
                        .font(.footnote)
                }
                Divider()
                
                if (noteEditable && currentAct.type == "task") {
                    DatePicker("", selection: $currentAct.createdAt, displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .labelsHidden()
                }
                
                
                if (currentAct.note == nil || currentAct.note == "" || noteEditable) {
                    VStack {
                        TextEditor(text: $currentActNote)
                            .frame(height: 100, alignment: .center)
                            .cornerRadius(8)
                            .padding(.bottom, 20)
                        
                        HStack {
                            
                            if (!noteEditable && currentAct.type == "task") {
                                Button {
                                    currentActNote = currentAct.note ?? ""
                                    noteEditable = true
                                } label: {
                                    Text("edit")
                                }
                            }

                            Button {
                                currentAct.note = currentActNote
                                try? currentAct.managedObjectContext?.save()
                                dataSource.clearAndReload()
                                noteEditable = false
                            } label: {
                                Text("save")
                            }
                            Button {
                                noteEditable = false
                                currentAct.createdAt = originalActDate
                            } label: {
                                Text("cancel")
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .padding([.top, .trailing], 20)
                        .padding([.bottom], 10)
                    }
                } else {
                    VStack {
                        Text(currentAct.note ?? "")
                            .frame(maxWidth: .infinity, alignment: .leading)
                        Button {
                            currentActNote = currentAct.note ?? ""
                            noteEditable = true
                        } label: {
                            Text("edit")
                        }
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .padding([.trailing], 20)
                        .padding([.bottom], 10)
                    }
                }
                

            }
            .padding(20)
            .coordinateSpace(name: "stack")
            .background(.ultraThinMaterial)
            .cornerRadius(20)
            .padding(20)
        }
        .onAppear(){
            originalActDate = currentAct.createdAt
        }
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }
}

