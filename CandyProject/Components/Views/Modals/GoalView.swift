//
//  SuggestionView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/2.
//

import CoreData
import SwiftUI

struct GoalView: View {
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    @AppStorage("showGoalView") var showGoalView = true
    @AppStorage("mainViewName") var mainViewName : ViewType = .home
    @Environment(\.dismiss) private var dismiss
    var dataSource : ContentDataSource
    var predicate : NSPredicate
    @State var goalTag: Tag?
    @State var quantity = 10
    @State var startDate: Date = Date()
    @State var endDate: Date = Calendar.current.date(byAdding: .day, value: 1, to: Date())!
    @State var point = 600
    @State var calendarId: UUID = UUID()
    var calendar = Calendar.current
    
    var onCreate: () -> Void = {}
    
    var body: some View {
        ZStack {
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
                .onAppear() {
                    dataSource.loadTagList(predicate: predicate)
                }
            
            CloseButton(callback: onCreate)
            
            VStack {
                Text("SET UP A GOAL")
                    .font(.title)
                    .foregroundStyle(.linearGradient(colors: [Color.yellow, Color("green"), Color("pink")], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .shadow(color: .gray, radius: 1)
                
                Group {
                    form
                    
                    Button {
                        let newGoal = LongTermGoal(context: viewContext)
                        newGoal.id = UUID()
                        newGoal.point = Int16(point)
                        newGoal.startTime = calendar.startOfDay(for: startDate)
                        newGoal.endTime = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: endDate)!
                        newGoal.targetNo = Int16(quantity)
                        newGoal.status = "inprogress"
                        newGoal.tagId = goalTag!.id
                        newGoal.currentNo = 0
                        dataSource.addGoal(goal: newGoal)
                        calendarId = UUID()
                        showGoalView = false
                        mainViewName = .home
                        onCreate()
                    } label: {
                        Text("Create Goal").modifier(ButtonGradientModifier())
                    }.padding(.all, 40.0)
                }
            }
        }
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }
    
    var form: some View {
        VStack {
            
            VStack {
                Text("Choose a Task:")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                Picker("", selection: $goalTag) {
                    ForEach(dataSource.getTagListByType(type: "pain")) { tag in
                        Text(tag.name).tag(tag as Tag?)
                    }
                }
                .accentColor(.black)
                .onAppear{
                    if (!dataSource.getTagListByType(type: "pain").isEmpty) {
                        goalTag = dataSource.getTagListByType(type: "pain")[0]
                    }
                }
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            VStack {
                Text("Number of Tasks:")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                TextField("", value: $quantity, formatter: NumberFormatter())
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                    .padding(.horizontal, 20.0)
            }
            .modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            VStack {
                Text("Start Date:")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                DatePicker("", selection: $startDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .frame(maxWidth: .infinity, alignment: .center)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            VStack {
                Text("End Date:")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                DatePicker("", selection: $endDate, in: startDate..., displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .id(calendarId)
                    .frame(maxWidth: .infinity, alignment: .center)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            VStack {
                Text("Point:")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                TextField("", value: $point, formatter: NumberFormatter())
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                    .padding(.horizontal, 20.0)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
        }
    }
}

