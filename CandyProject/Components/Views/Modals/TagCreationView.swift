//
//  TagCreationView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/16.
//

import SwiftUI

struct TagCreationView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @AppStorage("showCreateTag") var showCreateTag = true
    @AppStorage("actType") var actType : TagType = .pain
    @ObservedObject var dataSource: ContentDataSource
    var isEdit: Bool
    var itemTobeEdited: Tag?
    var onTagEdit: (_ tag: Tag?) -> Void
    var onTagAdd: (_ tag: Tag) -> Void
    
    @State var tagname = ""
    @State var tagpoint = 100
    var body: some View {
        ZStack{

            // Background gradient
            LinearGradient(
                colors: actType == .gain ?
                    [.candyPinkLight, .backgroundPrimary] :
                    [.taskGreenLight, .backgroundPrimary],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            // Header
            VStack(spacing: 0) {
                HStack {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.textPrimary)
                            .frame(width: 32, height: 32)
                            .background(Color.white)
                            .clipShape(Circle())
                    }

                    Spacer()

                    Text(isEdit ?
                         (actType == .gain ? "编辑糖果 / Edit Candy" : "编辑任务 / Edit Task") :
                         (actType == .gain ? "创建糖果 / Create Candy" : "创建任务 / Create Task"))
                        .font(Typography.title2)
                        .foregroundColor(.textPrimary)

                    Spacer()

                    // Placeholder for symmetry
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
                .padding(.bottom, Spacing.lg)
                .background(.ultraThinMaterial)
                .cornerRadius(CornerRadius.xl, corners: [.bottomLeft, .bottomRight])

                Spacer()
            }

            VStack(spacing: Spacing.xl) {
                // Icon Section
                VStack(spacing: Spacing.md) {
                    ZStack {
                        Circle()
                            .fill((actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.2))
                            .frame(width: 80, height: 80)

                        Image(systemName: actType == .gain ? "gift.fill" : "checkmark.circle.fill")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(actType == .gain ? .candyPink : .taskGreen)
                    }

                    Text(actType == .gain ? "创建新的糖果奖励" : "创建新的任务挑战")
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 100)
                
                form
                    .onAppear {
                        if ((itemTobeEdited) != nil) {
                            tagname = itemTobeEdited!.name
                            tagpoint = Int(itemTobeEdited!.point)
                        }
                    }

            }
            .padding(.horizontal, Spacing.lg)
            
            
        }
        .navigationBarHidden(true)
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
        
    }
    var form: some View {
        VStack(spacing: Spacing.lg) {
            // Name Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(actType == .gain ? "糖果名称 / Candy Name" : "任务名称 / Task Name")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)

                TextField(
                    actType == .gain ? "例如：一杯奶茶" : "例如：运动30分钟",
                    text: $tagname
                )
                .font(Typography.bodyMedium)
                .foregroundColor(.textPrimary)
                .padding(Spacing.md)
                .background(Color.backgroundSecondary)
                .cornerRadius(CornerRadius.lg)
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.lg)
                        .stroke((actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3), lineWidth: 1)
                )
            }

            // Point Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(actType == .gain ? "糖果价格 / Candy Price" : "任务积分 / Task Points")
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)

                TextField("100", value: $tagpoint, formatter: NumberFormatter())
                    .font(Typography.bodyMedium)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                    .padding(Spacing.md)
                    .background(Color.backgroundSecondary)
                    .cornerRadius(CornerRadius.lg)
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.lg)
                            .stroke((actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3), lineWidth: 1)
                    )
            }
            
            // Action Button
            Button {
                guard self.tagname != "" else {return}

                if (itemTobeEdited != nil) {
                    dataSource.objectWillChange.send()
                    dataSource.editTag(tagTobeEdited: itemTobeEdited!, name: tagname, point: tagpoint)
                    itemTobeEdited!.name = tagname
                    itemTobeEdited!.point = Int16(tagpoint)
                    onTagEdit(itemTobeEdited)
                    HapticFeedback.success()
                    dismiss()
                } else {
                    let newTag = Tag(context: viewContext)
                    newTag.id = UUID()
                    newTag.name = self.tagname
                    newTag.type = actType.rawValue
                    newTag.point = Int16(self.tagpoint)
                    dataSource.addTag(tag: newTag)
                    onTagAdd(newTag)
                    HapticFeedback.success()
                    dismiss()
                }
            } label: {
                Text(isEdit ? "保存更改 / Save Changes" : (actType == .gain ? "创建糖果 / Create Candy" : "创建任务 / Create Task"))
                    .font(Typography.bodyMedium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.lg)
                    .background(
                        tagname.isEmpty ?
                        LinearGradient(colors: [.gray], startPoint: .leading, endPoint: .trailing) :
                        LinearGradient(
                            colors: actType == .gain ? [.candyPink, .primaryPurple] : [.taskGreen, .primaryBlue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(CornerRadius.xl)
                    .shadow(
                        color: tagname.isEmpty ? .clear : (actType == .gain ? Color.candyPink : Color.taskGreen).opacity(0.3),
                        radius: 8, x: 0, y: 4
                    )
            }
            .disabled(tagname.isEmpty)

        }
        
    }
    
    
}

extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {
            ZStack(alignment: alignment) {
                placeholder().opacity(shouldShow ? 1 : 0)
                self
            }
        }
}
