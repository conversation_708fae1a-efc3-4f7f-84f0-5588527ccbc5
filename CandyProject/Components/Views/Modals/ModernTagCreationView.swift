//
//  ModernTagCreationView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/24.
//

import SwiftUI
import CoreData

struct ModernTagCreationView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @AppStorage("actType") var actType: TagType = .pain
    @ObservedObject var dataSource: ContentDataSource
    
    var isEdit: Bool
    var itemTobeEdited: Tag?
    var onTagEdit: (_ tag: Tag?) -> Void
    var onTagAdd: (_ tag: Tag) -> Void
    
    @State private var tagname = ""
    @State private var tagpoint = 100
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    private var title: String {
        if isEdit {
            return actType == .pain ? "编辑任务" : "编辑糖果"
        } else {
            return actType == .pain ? "创建新任务" : "创建新糖果"
        }
    }
    
    private var nameLabel: String {
        actType == .pain ? "任务名称" : "糖果名称"
    }
    
    private var pointLabel: String {
        actType == .pain ? "任务积分" : "糖果积分"
    }
    
    private var saveButtonText: String {
        isEdit ? "保存更改" : "创建"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: actType == .pain ? 
                        [.taskGreenLight, .backgroundPrimary] : 
                        [.candyPinkLight, .backgroundPrimary],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Form content
                    formSection
                    
                    Spacer()
                    
                    // Action buttons
                    actionButtonsSection
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                .padding(.bottom, 40)
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            if let item = itemTobeEdited {
                tagname = item.name
                tagpoint = Int(item.point)
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var headerSection: some View {
        HStack {
            Button {
                dismiss()
            } label: {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(.textSecondary)
                    .background(Color.white)
                    .clipShape(Circle())
            }
            
            Spacer()
            
            Text(title)
                .font(.title2.weight(.semibold))
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            // Invisible button for balance
            Button {} label: {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(.clear)
            }
        }
    }
    
    private var formSection: some View {
        VStack(spacing: 20) {
            // Name input
            VStack(alignment: .leading, spacing: 8) {
                Text(nameLabel)
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                TextField("请输入名称", text: $tagname)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(.body)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
            
            // Points input
            VStack(alignment: .leading, spacing: 8) {
                Text(pointLabel)
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                HStack {
                    TextField("积分", value: $tagpoint, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.body)
                        .keyboardType(.numberPad)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    
                    Text("分")
                        .font(.body)
                        .foregroundColor(.textSecondary)
                }
            }
            
            // Points slider
            VStack(alignment: .leading, spacing: 8) {
                Text("快速选择积分")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                
                Slider(value: Binding(
                    get: { Double(tagpoint) },
                    set: { tagpoint = Int($0) }
                ), in: 10...500, step: 10)
                .accentColor(actType == .pain ? .taskGreen : .candyPink)
                
                HStack {
                    Text("10")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text("500")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Save button
            Button {
                saveTag()
            } label: {
                HStack {
                    Image(systemName: isEdit ? "checkmark.circle.fill" : "plus.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                    
                    Text(saveButtonText)
                        .font(.body.weight(.semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: actType == .pain ? 
                            [.taskGreen, .primaryBlue] : 
                            [.candyPink, .primaryPurple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
                .shadow(
                    color: (actType == .pain ? .taskGreen : .candyPink).opacity(0.3),
                    radius: 8, x: 0, y: 4
                )
            }
            .disabled(tagname.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            
            // Cancel button
            Button {
                dismiss()
            } label: {
                Text("取消")
                    .font(.body.weight(.medium))
                    .foregroundColor(.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            }
        }
    }
    
    private func saveTag() {
        let trimmedName = tagname.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty else {
            alertMessage = "请输入\(actType == .pain ? "任务" : "糖果")名称"
            showingAlert = true
            return
        }
        
        guard tagpoint > 0 else {
            alertMessage = "积分必须大于0"
            showingAlert = true
            return
        }
        
        if isEdit, let item = itemTobeEdited {
            // Edit existing tag
            item.name = trimmedName
            item.point = Int16(tagpoint)
            onTagEdit(item)
        } else {
            // Create new tag
            let newTag = Tag(context: viewContext)
            newTag.name = trimmedName
            newTag.point = Int16(tagpoint)
            newTag.type = actType.rawValue
            newTag.id = UUID()
            newTag.orderIndex = 0
            
            dataSource.addTag(tag: newTag)
            onTagAdd(newTag)
        }
        
        dismiss()
    }
}

// MARK: - Color Extensions
extension Color {
    static let taskGreen = Color(red: 0.2, green: 0.8, blue: 0.4)
    static let taskGreenLight = Color(red: 0.9, green: 0.98, blue: 0.93)
    static let candyPink = Color(red: 1.0, green: 0.4, blue: 0.6)
    static let candyPinkLight = Color(red: 1.0, green: 0.95, blue: 0.97)
    static let primaryBlue = Color.blue
    static let primaryPurple = Color.purple
    static let textPrimary = Color.primary
    static let textSecondary = Color.secondary
}
