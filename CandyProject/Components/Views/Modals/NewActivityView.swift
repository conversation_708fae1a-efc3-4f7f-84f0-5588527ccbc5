//
//  NewActivityView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/17.
//

import CoreData
import SwiftUI

struct TagRow: View {
    @ObservedObject var tag: Tag
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    var dataSource: ContentDataSource
    var onTagDelete: (_ tag: Tag) -> Void
    var onTagEdit: (_ tag: Tag?) -> Void
    var onTagAdd: (_ tag: Tag) -> Void
    
    @Binding var editMode: EditMode
    @State var tagNotDeletable = false
    @State var itemToBeEdited : Tag?
    @AppStorage("showCreatePainAct") var showCreatePainAct = false
    @AppStorage("showCreateGainAct") var showCreateGainAct = false
    @AppStorage("actType") var actType : TagType = .pain
    
    var body: some View {
        HStack {
            Button {
                let newActivity = Activity(context: viewContext)
                newActivity.point = tag.point
                newActivity.tagName = tag.name
                newActivity.createdAt = Date()
                newActivity.tagType = actType == .pain ? "pain" :  "gain"
                newActivity.id = UUID()
                newActivity.tagId = tag.id
                newActivity.type = "task"
                dataSource.actionActivity(act: newActivity)
                dataSource.addActivity(act: newActivity)
                showCreateGainAct = false;
                showCreatePainAct = false;
            } label: {
                GeometryReader { metrics in
                    
                    HStack {
                        Text("\(tag.name)")
                            .foregroundColor(Color.black)
                            .padding(10)
                            .modifier(ListModifier(
                                width: editMode == .inactive ? metrics.size.width * 0.8 : metrics.size.width,
                                actType: TagType(rawValue: tag.type) ?? .pain))
                        
                        if (editMode == .inactive) {
                            Text("\(tag.type == "pain" ? "+" : "-") \(tag.point)")
                                .foregroundColor(Color.black)
                                .modifier(ListModifier(width: metrics.size.width * 0.2, actType: TagType(rawValue: tag.type) ?? .pain))
                        }
                        
                    }
                    .frame(maxHeight: .infinity, alignment: .center)
                    
                }
            }
            
        }
        .swipeActions {
            Button {
                
                if (tag.type == "pain") {
                    for goal in dataSource.goals {
                        if (goal.tagId == tag.id) {
                            tagNotDeletable = true
                            break
                        }
                    }
                }
                
                guard !tagNotDeletable else {
                    return
                }
                
                onTagDelete(tag)
                
                dataSource.deleteTag(tag: tag)
                
            } label: {
                Image(systemName: "trash")
            }
            .tint(.red)
            
            Button {
                
                if (tag.type == "pain") {
                    for goal in dataSource.goals {
                        if (goal.tagId == tag.id) {
                            tagNotDeletable = true
                        }
                    }
                }
                
                guard !tagNotDeletable else {
                    return
                }
                
                itemToBeEdited = tag
            } label: {
                Image(systemName: "pencil")
            }
            .tint(.blue)
        }
        .sheet(item: $itemToBeEdited, content: { itemToBeEdited in
            TagCreationView(dataSource: dataSource, isEdit: true, itemTobeEdited: itemToBeEdited, onTagEdit: onTagEdit, onTagAdd: onTagAdd)
        })
        .alert("Task cannot be deleted or edited when it is used in a goal", isPresented: $tagNotDeletable) {
            Button("OK", role: .cancel) { }
        }
    }
    
}

struct NewActivityView: View {
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    @AppStorage("showCreatePainAct") var showCreatePainAct = false
    @AppStorage("showCreateGainAct") var showCreateGainAct = false
    @AppStorage("actType") var actType : TagType = .pain
    @AppStorage("showCreateTag") var showCreateTag = false
    @AppStorage("donereview") var donereview = false
    @State var editMode: EditMode = .inactive
    var predicate: NSPredicate
    var dataSource : ContentDataSource
    @State var taglist: [Tag] = []

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: actType == .pain ?
                        [.green.opacity(0.1), Color(.systemBackground)] :
                        [.pink.opacity(0.1), Color(.systemBackground)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header
                    headerSection

                    // Content
                    if taglist.isEmpty {
                        emptyStateView
                    } else {
                        tagListSection
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            dataSource.loadTagList(predicate: predicate)
            taglist = dataSource.getTagListByType(type: actType.rawValue)
        }
    }

    private var headerSection: some View {
        VStack(spacing: 24) {
            // Close button and title
            HStack {
                Button {
                    showCreatePainAct = false
                    showCreateGainAct = false
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.secondary)
                        .background(Color.white)
                        .clipShape(Circle())
                }

                Spacer()

                Text(actType == .pain ? "选择任务" : "选择糖果")
                    .font(.title.weight(.semibold))
                    .foregroundColor(.primary)

                Spacer()

                // Invisible button for balance
                Button {} label: {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.clear)
                }
            }
            .padding(.horizontal, 24)
            .padding(.top, 24)

            // Create new button
            Button {
                showCreateTag = true
                if (!donereview && (dataSource.visitCount == 2 || dataSource.visitCount == 20 || dataSource.visitCount == 100 || dataSource.visitCount == 200)) {
                    ReviewHandler.requestReview()
                    donereview = true
                }
            } label: {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20, weight: .medium))

                    Text(actType == .pain ? "创建新任务" : "创建新糖果")
                        .font(.body.weight(.medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: actType == .pain ?
                            [.green, .blue] :
                            [.pink, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(20)
                .shadow(
                    color: (actType == .pain ? Color.green : Color.pink).opacity(0.3),
                    radius: 8, x: 0, y: 4
                )
            }
            .sheet(isPresented: $showCreateTag) {
                TagCreationView(dataSource: dataSource, isEdit: false, onTagEdit: editTag, onTagAdd: addTag)
            }

            // Edit mode toggle
            if !taglist.isEmpty {
                HStack {
                    Spacer()

                    Button {
                        if editMode == .inactive {
                            editMode = .active
                            for tag in taglist {
                                dataSource.updateTagOrder(tag: tag, orderIndex: Int16(taglist.firstIndex(of: tag)!))
                            }
                            dataSource.save()
                        } else {
                            editMode = .inactive
                            dataSource.loadTagList(predicate: NSPredicate(value: true))
                            taglist = dataSource.getTagListByType(type: actType.rawValue)
                        }
                    } label: {
                        Text(editMode == .inactive ? "重新排序" : "完成")
                            .font(.callout)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal, 24)
            }
        }
        .padding(.bottom, 24)
        .background(.ultraThinMaterial)
        .cornerRadius(20, corners: [.bottomLeft, .bottomRight])
    }

    private var emptyStateView: some View {
        VStack(spacing: 32) {
            Spacer()

            Image(systemName: actType == .pain ? "checkmark.circle" : "gift")
                .font(.system(size: 80, weight: .light))
                .foregroundColor(actType == .pain ? .green : .pink)

            VStack(spacing: 8) {
                Text(actType == .pain ? "还没有任务" : "还没有糖果")
                    .font(.title2)
                    .foregroundColor(.primary)

                Text("创建你的第一个\(actType == .pain ? "任务" : "糖果")并设置积分")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding(.horizontal, 32)
    }

    private var tagListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(taglist) { tag in
                    if !tag.isFault {
                        tagRow(tag: tag)
                            .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.top, 16)
            .padding(.bottom, 48)
        }
        .environment(\.editMode, .constant(editMode))
    }

    func tagRow(tag: Tag) -> some View {
        Button {
            if editMode == .inactive {
                createActivity(with: tag)
            }
        } label: {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: actType == .pain ? "checkmark.circle.fill" : "gift.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(actType == .pain ? .green : .pink)
                    .frame(width: 32, height: 32)

                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(tag.name)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Points
                if editMode == .inactive {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(actType == .pain ? "+" : "-")\(tag.point)")
                            .font(.headline)
                            .foregroundColor(actType == .pain ? .green : .pink)

                        Text("积分")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(16)
            .background(
                actType == .pain ?
                    Color.green.opacity(0.1) :
                    Color.pink.opacity(0.1)
            )
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        (actType == .pain ? Color.green : Color.pink).opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func createActivity(with tag: Tag) {
        let newActivity = Activity(context: viewContext)
        newActivity.point = tag.point
        newActivity.tagName = tag.name
        newActivity.createdAt = Date()
        newActivity.tagType = actType.rawValue
        newActivity.id = UUID()
        newActivity.tagId = tag.id
        newActivity.type = "task"

        dataSource.actionActivity(act: newActivity)
        dataSource.addActivity(act: newActivity)

        showCreateGainAct = false
        showCreatePainAct = false
    }

    // MARK: - Helper Functions
    private func deleteTag(tag: Tag) {
        for tagloop in taglist {
            if tagloop.id == tag.id {
                if let i = taglist.firstIndex(of: tagloop) {
                    taglist.remove(at: i)
                    break
                }
            }
        }
    }

    private func editTag(tag: Tag?) {
        if let tag = tag {
            for tagloop in taglist {
                if tag.id == tagloop.id {
                    tagloop.name = tag.name
                    tagloop.point = tag.point
                    break
                }
            }
        }
    }

    private func addTag(tag: Tag) {
        taglist.insert(tag, at: 0)
    }

    
}

//struct NewActivityView_Previews: PreviewProvider {
//    static var previews: some View {
//        NewActivityView(predicate: NSPredicate(format: "type == %@", "pain"))
//            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
//    }
//}
