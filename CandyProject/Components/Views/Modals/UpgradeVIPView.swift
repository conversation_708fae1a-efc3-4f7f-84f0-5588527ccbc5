//
//  UpgradeVIP.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/23.
//

import SwiftUI

struct UpgradeVIPView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @AppStorage("showUpgradeVIP") var showUpgradeVIP = true
    var dataSource: ContentDataSource
    var storeManager: StoreManager
    var gVar: GlobalVar
    
    var privileges: some View {
        VStack {
            
            Label("ads free", systemImage: "speaker.slash.circle")
                .font(.headline)
                .modifier(FormLabelModifier())
                .modifier(ListModifier(height: 60, color: Color.clear.opacity(0.3)))
            
            Text("coming soon (no need to purchase again)")
                .font(.subheadline)
                .foregroundColor(Color.gray)
                .padding(20)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.leading, 40)
            
            Label("ios widget", systemImage: "square.and.pencil")
                .font(.headline)
                .modifier(FormLabelModifier())
                .modifier(ListModifier(height: 60, color: Color.clear.opacity(0.3)))
            
        }
    }
    var isVIP: some View {
        VStack {
            Text("Welcome, VIP customer")
                .font(.title)
                .foregroundColor(Color.gray)
                .shadow(color: .yellow, radius: 1)
                .padding(20)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text("privileges you have:")
                .font(.subheadline)
                .foregroundColor(Color.gray)
                .padding(20)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.leading, 40)
            
            privileges
        }
    }
    
    var notVIP: some View {
        VStack {
            
            
            Text("VIP exclusive privileges:")
                .font(.title)
                .foregroundColor(Color.gray)
                .shadow(color: .yellow, radius: 1)
                .padding(20)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            privileges
            
            Button {
                storeManager.purchaseProduct(product: storeManager.myProducts[0])
            } label: {
                Text("\(storeManager.myProducts[0].localizedPrice!)/permanent")
                    .modifier(ButtonGradientModifier())
            }
            .padding(.top, 20)
            .padding(20)
            
            Button {
                storeManager.restoreProducts()
            } label: {
                Text("RESTORE VIP")
                    .modifier(ButtonGradientModifier())
            }.padding(20)
            
        }
    }
    
    
    var body: some View {
        ZStack{
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
            
            
            CloseButton()
            
            if (dataSource.isVIP) {
                isVIP
                    .frame(maxHeight: .infinity, alignment: .top)
                    .padding(.top, 80)
            }
            
            if (!dataSource.isVIP) {
                notVIP
                    .frame(maxHeight: .infinity, alignment: .top)
                    .padding(.top, 80)
            }
            if (gVar.isLoading) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
            }
            
        }
        .onAppear {
            dataSource.getAppSettings()
        }
    }
}

//struct UpgradeVIPView_Previews: PreviewProvider {
//    static var previews: some View {
//        UpgradeVIPView()
//    }
//}
