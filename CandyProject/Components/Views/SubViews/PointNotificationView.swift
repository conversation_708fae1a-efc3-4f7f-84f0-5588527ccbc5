//
//  PointNotificationView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/22.
//

import SwiftUI

struct PointNotificationView: View {
    @State var notificationOn = false
    @State var minimumPoint : Int = 0
    @State var selectedDate = Date()
    @State var toShowAlert = false
    @State var isNegative = true
    
    var dataSource : ContentDataSource
    
    private let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
    
    
    init(dataSource : ContentDataSource){
        UITableView.appearance().backgroundColor = .clear
        UITableViewCell.appearance().backgroundColor = .clear
        self.dataSource = dataSource
    }
    
    var body: some View {
        ZStack {
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
            
            form
                .padding(.top, 40)
                .onTapGesture {
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
                .onAppear {
                    dataSource.updateBalance()
                }
        }

    }
    
    var form: some View {
        VStack {
            
            VStack {
                Text("Enable low point notification")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                Toggle("", isOn: $notificationOn)
                    .labelsHidden()
                    .frame(maxWidth: .infinity, alignment: .center)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            
            if (notificationOn) {
                VStack {
                    Text("Warning point")
                        .modifier(FormLabelModifier())
                    Divider().background(Color.black)
                    HStack {
                        Button {
                            isNegative = !isNegative
                        } label: {
                            Text("\(isNegative ? "-" : "+")")
                                .frame(width: 20, height: 20, alignment: .center)
                        }
                        TextField("", value: $minimumPoint, formatter: NumberFormatter())
                            .frame(width: 60, height: 30, alignment: .center)
                            .background(Color.gray.opacity(0.2).cornerRadius(5))
                            .keyboardType(.numberPad)
                    }
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20.0)
                }
                .modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
                .onAppear{
                    NotificationHandler.shared.requestPermission( onDeny: {
                        toShowAlert = true
                    })
                }
                .alert(isPresented : $toShowAlert){
                    Alert(title: Text("Notification has been disabled for this app"),
                          message: Text("Please go to settings to enable it now"),
                          primaryButton: .default(Text("Go To Settings")) {
                        self.goToSettings()
                    },
                          secondaryButton: .cancel() {
                        notificationOn = false
                    })
                }
                
                VStack {
                    Text("Daily notification time")
                        .modifier(FormLabelModifier())
                    Divider().background(Color.black)
                    DatePicker("", selection: $selectedDate, displayedComponents: .hourAndMinute)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .frame(maxWidth: .infinity, alignment: .center)
                }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
                
                
            }
            
            if (!dataSource.pointNotifications.isEmpty) {
                Text("You will receive notification at \(timeFormatter.string(from: dataSource.pointNotifications[0].triggertime)) on a daily basis when your point is lower than \(String(dataSource.pointNotifications[0].point))")
                    .padding(30)
            }
            
            VStack {
                Button {
                    if (notificationOn) {
                        dataSource.addNotification(
                            title: NSString.localizedUserNotificationString(forKey: "remaining_points", arguments: [dataSource.balance]),
                            subtitle: "Getup and earn some points",
                            type: .point, notiTime: selectedDate, notiPoint: isNegative ? -minimumPoint : minimumPoint)
                    } else {
                        dataSource.clearPointNotification()
                    }
                } label: {
                    Text("save").modifier(ButtonGradientModifier())
                }
                .frame(maxHeight: .infinity, alignment: .bottom)
                .padding(.bottom, 70)
                .padding(20)
                .onAppear {
                    dataSource.getPointNotifications()
                    if (!dataSource.pointNotifications.isEmpty) {
                        notificationOn = true
                        selectedDate = dataSource.pointNotifications[0].triggertime
                        isNegative = dataSource.pointNotifications[0].point < 0
                        minimumPoint =  isNegative ? -Int(dataSource.pointNotifications[0].point) : Int(dataSource.pointNotifications[0].point)
                    }
                }
            }
        }
        .frame(maxHeight: .infinity, alignment: .top)
    }
}

extension PointNotificationView {
    private func goToSettings(){
        DispatchQueue.main.async {
            UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
        }
    }
}
