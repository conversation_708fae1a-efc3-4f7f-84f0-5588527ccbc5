//
//  PreferenceView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/5/5.
//

import SwiftUI

struct PreferenceView: View {
    @AppStorage("startWithMon") var startWithMon = false
    
    var body: some View {
        ZStack {
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
            
            form
                .padding(.top, 40)
                .onTapGesture {
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
        }
        
        
    }
    
    var form: some View {
        VStack {
            
            VStack {
                Text("Calendar Start With Monday")
                    .modifier(FormLabelModifier())
                Divider().background(Color.black)
                Toggle("", isOn: $startWithMon)
                    .labelsHidden()
                    .frame(maxWidth: .infinity, alignment: .center)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            
        }
        .frame(maxHeight: .infinity, alignment: .top)
        
    }
}
