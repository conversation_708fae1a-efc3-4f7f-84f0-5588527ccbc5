//
//  ModernHomeView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/24.
//

import SwiftUI
import CoreData

struct ModernHomeView: View {
    @AppStorage("showCreateGainAct") var showCreateGainAct = false
    @AppStorage("actType") var actType: TagType = .pain
    @AppStorage("showGoalView") var showGoalView = false
    @State private var showCreateActivity = false
    @State private var goalDisabled = false
    @State private var selectedDate: Date = Date()
    @State private var selectedAct: Activity?
    @State private var progressForGoal: Double = 0
    @State private var goalExpanded: Bool = true
    @State private var showLanguageSelector = false
    @State private var currentLanguage = "中"
    @State private var showFilterMenu = false
    @State private var showCalendarView = false

    var dataSource: ContentDataSource
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    
    @State var longTermGoals: [LongTermGoal] = []
    
    init(dataSource: ContentDataSource) {
        UITableView.appearance().backgroundColor = .clear
        UITableViewCell.appearance().backgroundColor = .clear
        self.dataSource = dataSource
    }
    
    var body: some View {
        ZStack {
            // Background
            Color.backgroundPrimary
                .ignoresSafeArea()
            
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Header Section
                    headerSection
                        .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
                    
                    // Balance Card Section
                    balanceCardSection
                        .padding(.top, Spacing.lg)
                    
                    // Records Section
                    recordsSection
                        .padding(.top, Spacing.xl)
                    
                    // Bottom spacing for tab bar
                    Color.clear.frame(height: 120)
                }
            }
            .ignoresSafeArea(edges: .top)
            
            // Floating Add Button
            floatingAddButton
        }
        .sheet(isPresented: $showCreateActivity) {
            ModernCreateActivityView(dataSource: dataSource)
        }
        .onAppear {
            dataSource.updateBalance()
            dataSource.loadMoreContent()
            dataSource.loadTagList(predicate: NSPredicate(value: true))
            dataSource.loadGoals()
            longTermGoals = dataSource.goals
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack(spacing: Spacing.md) {
            // Logo and Title
            HStack(spacing: Spacing.sm) {
                Image("logo")
                    .resizable()
                    .frame(width: 32, height: 32)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                
                Text("CandyV2")
                    .font(Typography.title2)
                    .foregroundColor(.textPrimary)
            }
            
            Spacer()
            
            // Settings and Language
            HStack(spacing: Spacing.md) {
                Button {
                    // Settings action
                } label: {
                    Image(systemName: "gearshape")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.textSecondary)
                }
                
                Button {
                    showLanguageSelector.toggle()
                } label: {
                    Text("\(currentLanguage)/EN")
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textSecondary)
                        .padding(.horizontal, Spacing.sm)
                        .padding(.vertical, Spacing.xs)
                        .background(Color.backgroundSecondary)
                        .cornerRadius(CornerRadius.sm)
                }
            }
        }
        .padding(.horizontal, Spacing.lg)
        .responsiveLayout()
    }
    
    // MARK: - Balance Card Section
    private var balanceCardSection: some View {
        VStack(spacing: Spacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("总积分余额 / Total Points")
                        .font(Typography.subheadline)
                        .foregroundColor(.white.opacity(0.9))
                    
                    HStack(alignment: .bottom, spacing: Spacing.xs) {
                        Text("\(dataSource.balance)")
                            .font(.system(size: 56, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                        
                        Text("pts")
                            .font(Typography.title3)
                            .foregroundColor(.white.opacity(0.8))
                            .padding(.bottom, 8)
                    }
                }
                
                Spacer()
                
                Button {
                    // Location or info action
                } label: {
                    Image(systemName: "location")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .frame(width: 44, height: 44)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                }
            }
        }
        .padding(Spacing.xl)
        .background(
            LinearGradient(
                colors: [.candyPink, .primaryOrange],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.xl)
        .shadow(color: .candyPink.opacity(0.3), radius: 16, x: 0, y: 8)
        .padding(.horizontal, Spacing.lg)
        .scaleIn(delay: 0.2)
    }
    
    // MARK: - Records Section
    private var recordsSection: some View {
        VStack(spacing: Spacing.lg) {
            // Section Header
            HStack {
                Text("行为记录 / Records")
                    .font(Typography.title3)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                HStack(spacing: Spacing.sm) {
                    Button {
                        showFilterMenu.toggle()
                    } label: {
                        Image(systemName: "line.3.horizontal.decrease")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.textSecondary)
                    }
                    
                    Button {
                        showCalendarView.toggle()
                    } label: {
                        Image(systemName: "calendar")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.textSecondary)
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
            
            // Activities List
            if dataSource.activities.isEmpty {
                emptyStateView
            } else {
                activitiesListView
            }
        }
    }
    
    // MARK: - Activities List
    private var activitiesListView: some View {
        LazyVStack(spacing: Spacing.md) {
            ForEach(Array(dataSource.activities.prefix(10).enumerated()), id: \.element.id) { index, activity in
                ModernActivityRowCard(activity: activity) {
                    selectedAct = activity
                }
                .slideIn(delay: Double(index) * 0.1)
                .padding(.horizontal, Spacing.lg)
            }
        }
        .fullScreenCover(item: $selectedAct) { activity in
            DetailView(currentAct: activity, dataSource: dataSource)
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: "list.bullet.clipboard")
                .font(.system(size: 64, weight: .light))
                .foregroundColor(.textTertiary)
            
            VStack(spacing: Spacing.sm) {
                Text("还没有活动记录")
                    .font(Typography.title3)
                    .foregroundColor(.textPrimary)
                
                Text("点击右下角的 + 按钮开始记录")
                    .font(Typography.body)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(Spacing.xl)
        .frame(maxWidth: .infinity)
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.xl)
        .padding(.horizontal, Spacing.lg)
        .fadeIn(delay: 0.3)
    }
    
    // MARK: - Floating Add Button
    private var floatingAddButton: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Button {
                    showCreateActivity = true
                    HapticFeedback.medium()
                } label: {
                    Image(systemName: "plus")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 56, height: 56)
                        .background(
                            LinearGradient(
                                colors: [.candyPink, .primaryPurple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .clipShape(Circle())
                        .shadow(color: .candyPink.opacity(0.4), radius: 12, x: 0, y: 6)
                }
                .interactiveButton()
                .scaleIn(delay: 0.5)
                .padding(.trailing, Spacing.lg)
                .padding(.bottom, 140) // Above tab bar
            }
        }
    }
}

// MARK: - Modern Activity Row Card
struct ModernActivityRowCard: View {
    let activity: Activity
    let onTap: () -> Void
    
    private var tagType: TagType {
        TagType(rawValue: activity.tagType) ?? .pain
    }
    
    private var iconName: String {
        switch tagType {
        case .pain:
            return "checkmark.circle.fill"
        case .gain:
            return "minus.circle.fill"
        }
    }
    
    private var iconColor: Color {
        switch tagType {
        case .pain:
            return .taskGreen
        case .gain:
            return .candyPink
        }
    }
    
    private var pointPrefix: String {
        tagType == .pain ? "+" : "-"
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: Spacing.md) {
                // Icon
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 48, height: 48)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                // Content
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(activity.tagName)
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(formatDateTime(activity.createdAt))
                        .font(Typography.caption)
                        .foregroundColor(.textTertiary)
                    
                    if let note = activity.note, !note.isEmpty {
                        Text(note)
                            .font(Typography.footnote)
                            .foregroundColor(.textSecondary)
                            .lineLimit(1)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // Points
                Text("\(pointPrefix)\(activity.point)")
                    .font(Typography.headline)
                    .foregroundColor(iconColor)
            }
            .padding(Spacing.md)
            .background(Color.backgroundSecondary)
            .cornerRadius(CornerRadius.lg)
        }
        .interactiveButton()
    }
    
    private func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - Preview
struct ModernHomeView_Previews: PreviewProvider {
    static var previews: some View {
        ModernHomeView(dataSource: ContentDataSource())
            .preferredColorScheme(.light)
    }
}
