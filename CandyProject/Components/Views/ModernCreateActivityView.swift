//
//  ModernCreateActivityView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/24.
//

import SwiftUI
import CoreData

struct ModernCreateActivityView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("actType") var actType: TagType = .gain
    @AppStorage("showCreateTag") var showCreateTag = false
    @State private var selectedTag: Tag?
    @State private var note: String = ""
    @State private var selectedDate: Date = Date()
    @State private var taglist: [Tag] = []
    @State private var showDatePicker = false
    @State private var showAddCandy = false
    @State private var showAddTask = false

    let dataSource: ContentDataSource
    private var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext

    init(dataSource: ContentDataSource) {
        self.dataSource = dataSource
    }

    private var title: String {
        "创建记录 / Create Record"
    }

    private var candyTags: [Tag] {
        taglist.filter { $0.type == "gain" }
    }

    private var taskTags: [Tag] {
        taglist.filter { $0.type == "pain" }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: actType == .gain ?
                        [.candyPinkLight, .backgroundPrimary] :
                        [.taskGreenLight, .backgroundPrimary],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header
                    headerSection

                    // Content
                    ScrollView {
                        VStack(spacing: Spacing.lg) {
                            // Type Selector
                            typeSelectorSection

                            // Add Button Section
                            addButtonSection

                            // Preset Options
                            presetOptionsSection

                            // Note Section
                            noteSection

                            // Date & Time Section
                            dateTimeSection

                            // Save Button
                            saveButtonSection

                            Spacer(minLength: 100)
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.top, Spacing.lg)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            loadTags()
        }
        .sheet(isPresented: $showAddCandy) {
            TagCreationView(
                dataSource: dataSource,
                isEdit: false,
                itemTobeEdited: nil,
                onTagEdit: { _ in },
                onTagAdd: { _ in loadTags() }
            )
            .onAppear {
                actType = .gain
            }
        }
        .sheet(isPresented: $showAddTask) {
            TagCreationView(
                dataSource: dataSource,
                isEdit: false,
                itemTobeEdited: nil,
                onTagEdit: { _ in },
                onTagAdd: { _ in loadTags() }
            )
            .onAppear {
                actType = .pain
            }
        }

    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            HStack {
                Spacer()

                Text(title)
                    .font(Typography.title2)
                    .foregroundColor(.textPrimary)

                Spacer()

                Button {
                    dismiss()
                } label: {
                    Image(systemName: "xmark")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(Color.white)
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
            .padding(.bottom, Spacing.lg)
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.bottomLeft, .bottomRight])
        }
        .fadeIn()
    }
    
    // MARK: - Type Selector Section
    private var typeSelectorSection: some View {
        HStack(spacing: 0) {
            // Candy Button
            Button {
                withAnimation(.spring()) {
                    actType = .gain
                    selectedTag = nil
                }
                HapticFeedback.light()
            } label: {
                Text("糖果 / Candy")
                    .font(Typography.bodyMedium)
                    .foregroundColor(actType == .gain ? .white : .candyPink)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.md)
                    .background(
                        actType == .gain ?
                        LinearGradient(colors: [.candyPink, .primaryPurple], startPoint: .leading, endPoint: .trailing) :
                        LinearGradient(colors: [.clear], startPoint: .leading, endPoint: .trailing)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: CornerRadius.lg))
            }

            // Task Button
            Button {
                withAnimation(.spring()) {
                    actType = .pain
                    selectedTag = nil
                }
                HapticFeedback.light()
            } label: {
                Text("任务 / Task")
                    .font(Typography.bodyMedium)
                    .foregroundColor(actType == .pain ? .white : .taskGreen)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.md)
                    .background(
                        actType == .pain ?
                        LinearGradient(colors: [.taskGreen, .primaryBlue], startPoint: .leading, endPoint: .trailing) :
                        LinearGradient(colors: [.clear], startPoint: .leading, endPoint: .trailing)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: CornerRadius.lg))
            }
        }
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.lg)
        .slideIn(delay: 0.1)
    }

    // MARK: - Add Button Section
    private var addButtonSection: some View {
        Button {
            if actType == .gain {
                showAddCandy = true
            } else {
                showAddTask = true
            }
            HapticFeedback.light()
        } label: {
            HStack(spacing: Spacing.sm) {
                Image(systemName: "plus")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(actType == .gain ? .candyPink : .taskGreen)

                Text(actType == .gain ? "添加糖果 / Add Candy" : "添加任务 / Add Task")
                    .font(Typography.bodyMedium)
                    .foregroundColor(actType == .gain ? .candyPink : .taskGreen)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, Spacing.lg)
            .background(Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(
                        actType == .gain ? Color.candyPink : Color.taskGreen,
                        style: StrokeStyle(lineWidth: 2, dash: [8, 4])
                    )
            )
        }
        .interactiveButton()
        .slideIn(delay: 0.2)
    }
    
    // MARK: - Preset Options Section
    private var presetOptionsSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            let currentTags = actType == .gain ? candyTags : taskTags

            if currentTags.isEmpty {
                emptyTagsView
            } else {
                ForEach(Array(currentTags.enumerated()), id: \.element.id) { index, tag in
                    ModernPresetOptionCard(
                        tag: tag,
                        isSelected: selectedTag?.id == tag.id,
                        type: actType
                    ) {
                        withAnimation(.spring()) {
                            selectedTag = selectedTag?.id == tag.id ? nil : tag
                        }
                        HapticFeedback.light()
                    }
                    .slideIn(delay: Double(index) * 0.1 + 0.3)
                }
            }
        }
    }
    
    // MARK: - Empty Tags View
    private var emptyTagsView: some View {
        VStack(spacing: Spacing.md) {
            Image(systemName: actType == .gain ? "gift" : "checkmark.circle")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.textTertiary)
            
            Text(actType == .gain ? "还没有糖果选项" : "还没有任务选项")
                .font(Typography.body)
                .foregroundColor(.textSecondary)
            
            Text("请先在设置中创建一些选项")
                .font(Typography.footnote)
                .foregroundColor(.textTertiary)
        }
        .frame(maxWidth: .infinity)
        .padding(Spacing.xl)
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.lg)
        .fadeIn(delay: 0.3)
    }
    
    // MARK: - Note Section
    private var noteSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("备注 / Note")
                .font(Typography.bodyMedium)
                .foregroundColor(.textPrimary)
            
            if #available(iOS 16.0, *) {
                TextField("添加备注（可选）/ Add note (optional)", text: $note, axis: .vertical)
                    .font(Typography.body)
                    .foregroundColor(.textPrimary)
                    .padding(Spacing.md)
                    .background(Color.backgroundSecondary)
                    .cornerRadius(CornerRadius.md)
                    .lineLimit(3...6)
            } else {
                TextField("添加备注（可选）/ Add note (optional)", text: $note)
                    .font(Typography.body)
                    .foregroundColor(.textPrimary)
                    .padding(Spacing.md)
                    .background(Color.backgroundSecondary)
                    .cornerRadius(CornerRadius.md)
                    .lineLimit(3)
            }
        }
        .slideIn(delay: 0.4)
    }
    
    // MARK: - Date & Time Section
    private var dateTimeSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("日期时间 / Date & Time")
                .font(Typography.bodyMedium)
                .foregroundColor(.textPrimary)
            
            Button {
                showDatePicker.toggle()
            } label: {
                HStack {
                    Text(formatDateTime(selectedDate))
                        .font(Typography.body)
                        .foregroundColor(.textPrimary)
                    
                    Spacer()
                    
                    Image(systemName: "calendar")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.textSecondary)
                }
                .padding(Spacing.md)
                .background(Color.backgroundSecondary)
                .cornerRadius(CornerRadius.md)
            }
        }
        .slideIn(delay: 0.5)
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $selectedDate)
        }
    }
    
    // MARK: - Save Button Section
    private var saveButtonSection: some View {
        Button {
            saveActivity()
        } label: {
            Text("保存 / Save")
                .font(Typography.bodyMedium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, Spacing.lg)
                .background(
                    selectedTag != nil ?
                    LinearGradient(
                        colors: actType == .gain ? [.candyPink, .primaryPurple] : [.taskGreen, .primaryBlue],
                        startPoint: .leading,
                        endPoint: .trailing
                    ) :
                    LinearGradient(colors: [.gray], startPoint: .leading, endPoint: .trailing)
                )
                .cornerRadius(CornerRadius.xl)
                .shadow(
                    color: selectedTag != nil ? 
                    (actType == .gain ? .candyPink.opacity(0.3) : .taskGreen.opacity(0.3)) : 
                    .clear,
                    radius: 8, x: 0, y: 4
                )
        }
        .disabled(selectedTag == nil)
        .interactiveButton()
        .scaleIn(delay: 0.6)
    }
    
    // MARK: - Helper Methods
    private func loadTags() {
        dataSource.loadTagList(predicate: NSPredicate(value: true))
        taglist = dataSource.tags
    }
    
    private func saveActivity() {
        guard let tag = selectedTag else { return }
        
        let newActivity = Activity(context: viewContext)
        newActivity.point = tag.point
        newActivity.tagName = tag.name
        newActivity.createdAt = selectedDate
        newActivity.tagType = actType.rawValue
        newActivity.id = UUID()
        newActivity.tagId = tag.id
        newActivity.type = "task"
        newActivity.note = note.isEmpty ? nil : note
        
        dataSource.actionActivity(act: newActivity)
        dataSource.addActivity(act: newActivity)

        HapticFeedback.success()
        dismiss()
    }
    
    private func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy/MM/dd, HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - Modern Preset Option Card
struct ModernPresetOptionCard: View {
    let tag: Tag
    let isSelected: Bool
    let type: TagType
    let onTap: () -> Void

    private var iconName: String {
        type == .gain ? "gift.fill" : "checkmark.circle.fill"
    }

    private var pointPrefix: String {
        type == .gain ? "-" : "+"
    }

    private var accentColor: Color {
        type == .gain ? .candyPink : .taskGreen
    }

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: Spacing.md) {
                // Icon
                ZStack {
                    Circle()
                        .fill(accentColor)
                        .frame(width: 48, height: 48)

                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }

                // Content
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(tag.name)
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Points
                VStack(alignment: .trailing, spacing: Spacing.xs) {
                    Text("\(pointPrefix)\(tag.point)")
                        .font(Typography.headline)
                        .foregroundColor(accentColor)
                }

                // Arrow
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.textTertiary)
            }
            .padding(Spacing.md)
            .background(
                type == .gain ?
                Color.candyPinkLight :
                Color.taskGreenLight
            )
            .cornerRadius(CornerRadius.lg)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(
                        accentColor.opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
        .interactiveButton()
    }
}

// MARK: - Date Picker Sheet
struct DatePickerSheet: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedDate: Date
    
    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "选择日期时间",
                    selection: $selectedDate,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(.wheel)
                .labelsHidden()
                
                Spacer()
            }
            .padding()
            .navigationTitle("选择时间")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct ModernCreateActivityView_Previews: PreviewProvider {
    static var previews: some View {
        ModernCreateActivityView(dataSource: ContentDataSource())
            .preferredColorScheme(.light)
    }
}
