//
//  CloseButton.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/17.
//

import SwiftUI

struct CloseButton: View {
    @Environment(\.dismiss) private var dismiss
    
    var callback: () -> Void = {}
    
    var body: some View {
        
        Button{
            withAnimation {
                dismiss()
            }
            callback()
        } label: {
            Image(systemName: "xmark")
                .font(.system(size: 17, weight: .bold))
                .foregroundColor(.secondary)
                .padding(8)
                .background(.ultraThinMaterial, in: Circle())
        }
        .frame(maxWidth: .infinity, alignment: .topTrailing)
        .frame(maxHeight: .infinity, alignment: .topTrailing)
        .padding(26)
        
    }
}

struct CloseButton_Previews: PreviewProvider {
    static var previews: some View {
        CloseButton()
    }
}
