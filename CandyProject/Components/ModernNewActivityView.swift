//
//  ModernNewActivityView.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI
import CoreData

struct ModernNewActivityView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("actType") var actType: TagType = .pain
    @AppStorage("showCreateTag") var showCreateTag = false
    @AppStorage("donereview") var donereview = false
    
    @State private var editMode: EditMode = .inactive
    @State private var taglist: [Tag] = []
    
    let predicate: NSPredicate
    let dataSource: ContentDataSource
    
    private var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    
    private var title: String {
        actType == .pain ? "选择任务" : "选择糖果"
    }
    
    private var createButtonTitle: String {
        actType == .pain ? "创建新任务" : "创建新糖果"
    }
    
    private var emptyStateTitle: String {
        actType == .pain ? "还没有任务" : "还没有糖果"
    }
    
    private var emptyStateSubtitle: String {
        "创建你的第一个\(actType == .pain ? "任务" : "糖果")并设置积分"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: actType == .pain ? 
                        [.taskGreenLight, .backgroundPrimary] : 
                        [.candyPinkLight, .backgroundPrimary],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerSection
                    
                    // Content
                    if taglist.isEmpty {
                        emptyStateView
                    } else {
                        tagListSection
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            dataSource.loadTagList(predicate: predicate)
            taglist = dataSource.getTagListByType(type: actType.rawValue)
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: Spacing.lg) {
            // Close button and title
            HStack {
                Button {
                    dismiss()
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.textSecondary)
                        .background(Color.white)
                        .clipShape(Circle())
                }
                
                Spacer()
                
                Text(title)
                    .font(Typography.title1)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                // Invisible button for balance
                Button {} label: {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.clear)
                }
            }
            .responsiveLayout()
            .padding(.top, SafeAreaHandler.getTopSafeArea() + Spacing.md)
            .dynamicIslandAware()
            
            // Create new button
            Button {
                showCreateTag = true
                if (!donereview && (dataSource.visitCount == 2 || dataSource.visitCount == 20 || dataSource.visitCount == 100 || dataSource.visitCount == 200)) {
                    ReviewHandler.requestReview()
                    donereview = true
                }
            } label: {
                HStack(spacing: Spacing.sm) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                    
                    Text(createButtonTitle)
                        .font(Typography.bodyMedium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, Spacing.xl)
                .padding(.vertical, Spacing.md)
                .background(
                    LinearGradient(
                        colors: actType == .pain ? 
                            [.taskGreen, .primaryBlue] : 
                            [.candyPink, .primaryPurple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(CornerRadius.xl)
                .shadow(
                    color: (actType == .pain ? .taskGreen : .candyPink).opacity(0.3),
                    radius: 8, x: 0, y: 4
                )
            }
            .interactiveButton()
            .sheet(isPresented: $showCreateTag) {
                ModernTagCreationView(dataSource: dataSource, isEdit: false, onTagEdit: editTag, onTagAdd: addTag)
            }
            
            // Edit mode toggle
            if !taglist.isEmpty {
                HStack {
                    Spacer()
                    
                    Button {
                        if editMode == .inactive {
                            editMode = .active
                            for tag in taglist {
                                dataSource.updateTagOrder(tag: tag, orderIndex: Int16(taglist.firstIndex(of: tag)!))
                            }
                            dataSource.save()
                        } else {
                            editMode = .inactive
                            dataSource.loadTagList(predicate: NSPredicate(value: true))
                            taglist = dataSource.getTagListByType(type: actType.rawValue)
                        }
                    } label: {
                        Text(editMode == .inactive ? "重新排序" : "完成")
                            .font(Typography.callout)
                            .foregroundColor(.primaryBlue)
                    }
                }
                .responsiveLayout()
            }
        }
        .padding(.bottom, Spacing.lg)
        .background(.ultraThinMaterial)
        .cornerRadius(CornerRadius.xl, corners: [.bottomLeft, .bottomRight])
        .slideIn(from: .top, delay: 0.1)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: Spacing.xl) {
            Spacer()
            
            Image(systemName: actType == .pain ? "checkmark.circle" : "gift")
                .font(.system(size: 80, weight: .light))
                .foregroundColor(actType == .pain ? .taskGreen : .candyPink)
            
            VStack(spacing: Spacing.sm) {
                Text(emptyStateTitle)
                    .font(Typography.title2)
                    .foregroundColor(.textPrimary)
                
                Text(emptyStateSubtitle)
                    .font(Typography.body)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .responsiveLayout()
        .fadeIn(delay: 0.5)
    }
    
    private var tagListSection: some View {
        ScrollView {
            LazyVStack(spacing: Spacing.sm) {
                ForEach(taglist) { tag in
                    if !tag.isFault {
                        ModernTagRow(
                            tag: tag,
                            dataSource: dataSource,
                            actType: actType,
                            editMode: editMode,
                            onTagDelete: deleteTag,
                            onTagEdit: editTag,
                            onTagAdd: addTag
                        )
                        .padding(.horizontal, Spacing.md)
                    }
                }
            }
            .padding(.top, Spacing.md)
            .padding(.bottom, Spacing.xxl)
        }
        .environment(\.editMode, .constant(editMode))
    }
    
    // MARK: - Helper Functions
    private func deleteTag(tag: Tag) {
        for tagloop in taglist {
            if tagloop.id == tag.id {
                if let i = taglist.firstIndex(of: tagloop) {
                    taglist.remove(at: i)
                    break
                }
            }
        }
    }
    
    private func editTag(tag: Tag?) {
        if let tag = tag {
            for tagloop in taglist {
                if tag.id == tagloop.id {
                    tagloop.name = tag.name
                    tagloop.point = tag.point
                    break
                }
            }
        }
    }
    
    private func addTag(tag: Tag) {
        taglist.insert(tag, at: 0)
    }
}

struct ModernTagRow: View {
    @ObservedObject var tag: Tag
    let dataSource: ContentDataSource
    let actType: TagType
    let editMode: EditMode
    let onTagDelete: (Tag) -> Void
    let onTagEdit: (Tag?) -> Void
    let onTagAdd: (Tag) -> Void

    @State private var tagNotDeletable = false
    @State private var itemToBeEdited: Tag?
    @AppStorage("showCreatePainAct") var showCreatePainAct = false
    @AppStorage("showCreateGainAct") var showCreateGainAct = false

    private var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext

    var body: some View {
        Button {
            if editMode == .inactive {
                createActivity()
            }
        } label: {
            HStack(spacing: Spacing.md) {
                // Icon
                Image(systemName: actType == .pain ? "checkmark.circle.fill" : "gift.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(actType == .pain ? .taskGreen : .candyPink)
                    .frame(width: 32, height: 32)

                // Content
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(tag.name)
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // Points
                if editMode == .inactive {
                    VStack(alignment: .trailing, spacing: Spacing.xs) {
                        Text("\(actType == .pain ? "+" : "-")\(tag.point)")
                            .font(Typography.headline)
                            .foregroundColor(actType == .pain ? .taskGreen : .candyPink)

                        Text("积分")
                            .font(Typography.caption)
                            .foregroundColor(.textSecondary)
                    }
                }
            }
            .padding(Spacing.md)
            .background(
                actType == .pain ?
                    Color.taskGreenLight :
                    Color.candyPinkLight
            )
            .cornerRadius(CornerRadius.lg)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(
                        (actType == .pain ? .taskGreen : .candyPink).opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
        .interactiveButton()
        .animatedCard()
        .swipeActions {
            Button {
                checkAndDelete()
            } label: {
                Image(systemName: "trash")
            }
            .tint(.red)

            Button {
                checkAndEdit()
            } label: {
                Image(systemName: "pencil")
            }
            .tint(.blue)
        }
        .sheet(item: $itemToBeEdited) { item in
            TagCreationView(
                dataSource: dataSource,
                isEdit: true,
                itemTobeEdited: item,
                onTagEdit: onTagEdit,
                onTagAdd: onTagAdd
            )
        }
        .alert("任务正在被目标使用，无法删除或编辑", isPresented: $tagNotDeletable) {
            Button("确定", role: .cancel) { }
        }
    }

    private func createActivity() {
        let newActivity = Activity(context: viewContext)
        newActivity.point = tag.point
        newActivity.tagName = tag.name
        newActivity.createdAt = Date()
        newActivity.tagType = actType.rawValue
        newActivity.id = UUID()
        newActivity.tagId = tag.id
        newActivity.type = "task"

        dataSource.actionActivity(act: newActivity)
        dataSource.addActivity(act: newActivity)

        showCreateGainAct = false
        showCreatePainAct = false
    }

    private func checkAndDelete() {
        if tag.type == "pain" {
            for goal in dataSource.goals {
                if goal.tagId == tag.id {
                    tagNotDeletable = true
                    return
                }
            }
        }

        onTagDelete(tag)
        dataSource.deleteTag(tag: tag)
    }

    private func checkAndEdit() {
        if tag.type == "pain" {
            for goal in dataSource.goals {
                if goal.tagId == tag.id {
                    tagNotDeletable = true
                    return
                }
            }
        }

        itemToBeEdited = tag
    }
}
