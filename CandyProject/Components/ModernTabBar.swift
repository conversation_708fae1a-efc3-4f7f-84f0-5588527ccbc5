//
//  ModernTabBar.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI

struct ModernTabBar: View {
    @Binding var selectedTab: ViewType
    @Binding var showCreateActivity: Bool
    @Binding var actType: TagType
    
    var body: some View {
        HStack(spacing: 0) {
            // Calendar Tab
            TabBarButton(
                icon: "calendar",
                title: "日历",
                isSelected: selectedTab == .calendar,
                action: { selectedTab = .calendar }
            )
            
            // Add Button (Center)
            AddButton(
                showCreateActivity: $showCreateActivity,
                actType: $actType
            )
            
            // Home Tab
            TabBarButton(
                icon: "house",
                title: "首页",
                isSelected: selectedTab == .home,
                action: { selectedTab = .home }
            )
            
            // Profile Tab
            TabBarButton(
                icon: "person.circle",
                title: "我的",
                isSelected: selectedTab == .setting,
                action: { selectedTab = .setting }
            )
        }
        .responsiveLayout()
        .padding(.top, Spacing.sm)
        .padding(.bottom, SafeAreaHandler.getBottomSafeArea() + Spacing.md)
        .iPhone16OptimizedTabBar()
    }
}

struct TabBarButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.xs) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(isSelected ? .primaryBlue : .textSecondary)
                    .scaleEffect(isSelected ? 1.1 : 1.0)
                
                Text(title)
                    .font(Typography.caption)
                    .foregroundColor(isSelected ? .primaryBlue : .textSecondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(isSelected ? Color.primaryBlue.opacity(0.1) : Color.clear)
            )
            .animation(AnimationPresets.spring, value: isSelected)
        }
        .interactiveButton()
    }
}

struct AddButton: View {
    @Binding var showCreateActivity: Bool
    @Binding var actType: TagType
    
    var body: some View {
        Button {
            actType = .pain
            showCreateActivity = true
        } label: {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.primaryBlue, .primaryPurple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 56, height: 56)
                    .shadow(color: .primaryBlue.opacity(0.3), radius: 8, x: 0, y: 4)
                
                Image(systemName: "plus")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.white)
            }
        }
        .interactiveButton()
        .pulse(duration: 2.0)
        .frame(maxWidth: .infinity)
    }
}

struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview
struct ModernTabBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            Spacer()
            ModernTabBar(
                selectedTab: .constant(.home),
                showCreateActivity: .constant(false),
                actType: .constant(.pain)
            )
        }
        .background(Color.backgroundPrimary)
    }
}
