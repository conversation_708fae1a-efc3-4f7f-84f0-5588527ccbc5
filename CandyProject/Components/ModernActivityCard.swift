//
//  ModernActivityCard.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI

struct ModernActivityCard: View {
    let activity: Activity
    let onTap: () -> Void
    
    private var tagType: TagType {
        TagType(rawValue: activity.tagType) ?? .pain
    }
    
    private var backgroundColor: Color {
        switch tagType {
        case .pain:
            return .taskGreenLight
        case .gain:
            return .candyPinkLight
        }
    }
    
    private var accentColor: Color {
        switch tagType {
        case .pain:
            return .taskGreen
        case .gain:
            return .candyPink
        }
    }
    
    private var iconName: String {
        switch tagType {
        case .pain:
            return "checkmark.circle.fill"
        case .gain:
            return "gift.fill"
        }
    }
    
    private var pointPrefix: String {
        tagType == .pain ? "+" : "-"
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: Spacing.md) {
                // Icon
                Image(systemName: iconName)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(accentColor)
                    .frame(width: 32, height: 32)
                
                // Content
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(activity.tagName)
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    if let note = activity.note, !note.isEmpty {
                        Text(note)
                            .font(Typography.footnote)
                            .foregroundColor(.textSecondary)
                            .lineLimit(2)
                    }
                    
                    Text(formatDate(activity.createdAt))
                        .font(Typography.caption)
                        .foregroundColor(.textTertiary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // Points
                VStack(alignment: .trailing, spacing: Spacing.xs) {
                    Text("\(pointPrefix)\(activity.point)")
                        .font(Typography.headline)
                        .foregroundColor(accentColor)
                    
                    Text("积分")
                        .font(Typography.caption)
                        .foregroundColor(.textSecondary)
                }
            }
            .padding(AdaptiveSpacing.cardPadding())
            .background(backgroundColor)
            .cornerRadius(CornerRadius.lg)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(accentColor.opacity(0.2), lineWidth: 1)
            )
        }
        .interactiveButton()
        .animatedCard()
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd HH:mm"
        return formatter.string(from: date)
    }
}

struct ModernActivitySection: View {
    let title: String
    let activities: [Activity]
    let onActivityTap: (Activity) -> Void
    let onDelete: (IndexSet) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            // Section Header
            HStack {
                Text(title)
                    .font(Typography.title3)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                Text("\(activities.count)项")
                    .font(Typography.caption)
                    .foregroundColor(.textSecondary)
                    .padding(.horizontal, Spacing.sm)
                    .padding(.vertical, Spacing.xs)
                    .background(Color.backgroundSecondary)
                    .cornerRadius(CornerRadius.sm)
            }
            .responsiveLayout()

            // Activities
            LazyVStack(spacing: AdaptiveSpacing.vertical()) {
                ForEach(Array(activities.enumerated()), id: \.element.id) { index, activity in
                    ModernActivityCard(activity: activity) {
                        onActivityTap(activity)
                    }
                    .responsiveLayout()
                    .slideIn(delay: Double(index) * 0.1)
                }
                .onDelete(perform: onDelete)
            }
        }
    }
}

struct ModernBalanceCard: View {
    let balance: Int
    let onCandyTap: () -> Void
    
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Balance Display
            VStack(spacing: Spacing.sm) {
                Text("当前积分")
                    .font(Typography.subheadline)
                    .foregroundColor(.textSecondary)
                
                Text("\(balance)")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.primaryBlue)
            }
            
            // Candy Button
            Button(action: onCandyTap) {
                HStack(spacing: Spacing.sm) {
                    Image(systemName: "gift.fill")
                        .font(.system(size: 20, weight: .medium))
                    
                    Text("兑换糖果")
                        .font(Typography.bodyMedium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, Spacing.xl)
                .padding(.vertical, Spacing.md)
                .background(
                    LinearGradient(
                        colors: [.candyPink, .primaryPurple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(CornerRadius.xl)
                .shadow(color: .candyPink.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .interactiveButton()
        }
        .padding(Spacing.xl)
        .background(
            LinearGradient(
                colors: [.white, .backgroundSecondary],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(CornerRadius.xl)
        .shadow(color: .black.opacity(0.1), radius: 16, x: 0, y: 8)
        .responsiveLayout()
        .scaleIn(delay: 0.2)
    }
}

struct ModernGoalCard: View {
    let goal: LongTermGoal
    let dataSource: ContentDataSource

    private var progress: Double {
        Double(goal.currentNo) / Double(goal.targetNo)
    }

    private var progressColor: Color {
        if progress >= 1.0 {
            return .taskGreen
        } else if progress >= 0.7 {
            return .primaryOrange
        } else {
            return .primaryBlue
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("\(goal.targetNo) x \(dataSource.getTagNameById(goal.tagId))")
                        .font(Typography.bodyMedium)
                        .foregroundColor(.textPrimary)

                    Text("奖励: +\(goal.point) 积分")
                        .font(Typography.footnote)
                        .foregroundColor(.taskGreen)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: Spacing.xs) {
                    Text("\(goal.currentNo)/\(goal.targetNo)")
                        .font(Typography.headline)
                        .foregroundColor(progressColor)

                    Text(formatDateRange(start: goal.startTime, end: goal.endTime))
                        .font(Typography.caption)
                        .foregroundColor(.textSecondary)
                }
            }

            // Progress Bar
            VStack(alignment: .leading, spacing: Spacing.xs) {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
                    .scaleEffect(x: 1, y: 2, anchor: .center)

                HStack {
                    Text("进度")
                        .font(Typography.caption)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    Text("\(Int(progress * 100))%")
                        .font(Typography.caption)
                        .foregroundColor(progressColor)
                }
            }
        }
        .padding(Spacing.md)
        .background(Color.backgroundSecondary)
        .cornerRadius(CornerRadius.lg)
        .overlay(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .stroke(progressColor.opacity(0.2), lineWidth: 1)
        )
    }

    private func formatDateRange(start: Date, end: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return "\(formatter.string(from: start)) - \(formatter.string(from: end))"
    }
}

// MARK: - Preview
struct ModernActivityCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: Spacing.md) {
            ModernBalanceCard(balance: 150) {
                // Candy tap action
            }
            
            ModernActivityCard(
                activity: createSampleActivity()
            ) {
                // Activity tap action
            }
            .padding(.horizontal, Spacing.md)
        }
        .background(Color.backgroundPrimary)
    }
    
    static func createSampleActivity() -> Activity {
        let context = PersistenceController.preview.container.viewContext
        let activity = Activity(context: context)
        activity.id = UUID()
        activity.tagName = "完成工作任务"
        activity.note = "今天完成了重要的项目里程碑"
        activity.point = 50
        activity.tagType = "pain"
        activity.createdAt = Date()
        activity.type = "task"
        return activity
    }
}
