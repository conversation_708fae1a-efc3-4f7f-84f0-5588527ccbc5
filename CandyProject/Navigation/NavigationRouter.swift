//
//  NavigationRouter.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI

// 定义所有可能的导航目标
enum NavigationDestination: Hashable {
    case createRecord
    case selectCandy
    case selectTask
    case createCandy
    case createTask
}

// 导航路由管理器
class NavigationRouter: ObservableObject {
    @Published var path = NavigationPath()
    
    // 导航到指定页面
    func navigate(to destination: NavigationDestination) {
        path.append(destination)
    }
    
    // 返回上一页
    func goBack() {
        if !path.isEmpty {
            path.removeLast()
        }
    }
    
    // 返回根页面
    func goToRoot() {
        path = NavigationPath()
    }
    
    // 替换当前页面
    func replace(with destination: NavigationDestination) {
        if !path.isEmpty {
            path.removeLast()
        }
        path.append(destination)
    }
}
