//
//  CandyProjectApp.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/16.
//

import SwiftUI
import GoogleMobileAds
import AppTrackingTransparency
import UserNotifications
import StoreKit

@main
struct CandyProjectApp: App {
    var gVar = GlobalVar()
    var ad : OpenAd
    @ObservedObject var dataSource = ContentDataSource()
    @ObservedObject var storeManager = StoreManager(gVar: GlobalVar(), dataSource: ContentDataSource())
    let productIDs = [
        "com.youran.CandyProject.vipcandy"
    ]
    
    
    init() {
        GADMobileAds.sharedInstance().requestConfiguration.testDeviceIdentifiers = [ "142E2744-08DE-47C6-9363-56B6498829F8", "08B795F6-EDE6-41CD-BE46-5D4EEA6F6594", "00008101-0006259C2E12001E"]
        GADMobileAds.sharedInstance().start(completionHandler: nil)
        ad = OpenAd(gVar)
        let visitCount = dataSource.getAndUpdateVisitTime()
        dataSource.getAppSettings()
        print("visit count: \(visitCount), vip: \(dataSource.isVIP)")
        storeManager = StoreManager(gVar: gVar, dataSource: dataSource)
        if (visitCount >= 5 && !dataSource.isVIP) {
            ad.tryToPresentAd()
        } else {
            gVar.showLoadingScreen = false
        }
    }
    
    let persistenceController = PersistenceController.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView(dataSource: dataSource, gVar: gVar, storeManager: storeManager)
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .onAppear(perform: {
                    storeManager.getProducts(productIDs: productIDs)
                    SKPaymentQueue.default().add(storeManager)
                })
        }
    }
}
