//
//  DataSource.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/25.
//

import SwiftUI
import CoreData

class ContentDataSource: ObservableObject {
    @Published var activities : [Activity] = []
    @Published var activitiesByDate : [Activity] = []
    @Published var activitiesBySearch : [Activity] = []
    @Published var tags : [Tag] = []
    @Published var goals : [LongTermGoal] = []
    @Published var pointNotifications : [LocalNotification] = []
    @Published var visitCount : Int16 = 0
    @Published var isVIP : Bool = false
    @Published var balance : Int = 0
    
    var currentOffset = 0
    private var canLoadMorePages = true
    var viewContext: NSManagedObjectContext = PersistenceController.shared.container.viewContext
    var calendar = Calendar.current
    
    func clearAndReload() {
        activities = []
        currentOffset = 0
        canLoadMorePages = true
        loadMoreContent()
    }
    
    func loadTagList(predicate: NSPredicate) {
        let request: NSFetchRequest<Tag> = Tag.fetchRequest()
        request.entity = Tag.entity()
        request.sortDescriptors = [NSSortDescriptor(key: "orderIndex", ascending: true)]
        request.predicate = predicate
        tags = try! viewContext.fetch(request)
    }
    
    func updateTagOrder(tag: Tag, orderIndex: Int16) {
        tag.orderIndex = orderIndex
    }
    
    func save() {
        try! viewContext.save()
    }
    
    func getTagListByType(type: String) -> [Tag] {
        var tagList : [Tag] = []
        for tag in tags {
            if tag.type == type {
                tagList.append(tag)
            }
        }
        return tagList
    }
    
    func addTag(tag: Tag) {
        tags.insert(tag, at: 0)
        try! viewContext.save()
    }
    
    func loadGoals() {
        let request: NSFetchRequest<LongTermGoal> = LongTermGoal.fetchRequest()
        request.entity = LongTermGoal.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \LongTermGoal.startTime, ascending: true)]
        request.predicate = NSPredicate(format: "status == %@", "inprogress")
        goals = try! viewContext.fetch(request)
    }
    
    func addGoal(goal: LongTermGoal) {
        goal.status = "inprogress"
        
        // trigger available activities
        for activity in activities {
            if (activity.tagId == goal.tagId
                && activity.createdAt >= goal.startTime
                && activity.createdAt <= goal.endTime) {
                goal.currentNo += 1
            }
        }
        
        if (goal.currentNo >= goal.targetNo) {
            goal.status = "done"
            
            // add new goal activity
            let newActivity = Activity(context: viewContext)
            newActivity.point = goal.point
            newActivity.tagName = "\(goal.targetNo) x \(getTagNameById(goal.tagId))"
            newActivity.createdAt = Date()
            newActivity.type = "goal"
            newActivity.id = UUID()
            newActivity.goalId = goal.id
            newActivity.tagType = "pain"
            actionActivity(act: newActivity)
            activities.insert(newActivity, at: 0)
            
            try! viewContext.save()
            return
        }
        
        if (goal.currentNo < goal.targetNo && goal.endTime <= Date()) {
            goal.status = "failed"
        }
        
        goals.append(goal)
        goals.sorted {
            $0.startTime < $1.startTime
        }
        try! viewContext.save()
    }
    
    func getTagNameById(_ tagid: UUID) -> String {
        for i in tags {
            if(i.id == tagid) {
                return i.name
            }
        }
        return ""
    }
    
    func getListModifier(act: Activity, width: CGFloat) -> ListModifier {
        if (act.newType == .goal) {
            return ListModifier(width: width, color: Color("red"))
        }
        return ListModifier(width: width, actType: TagType(rawValue: act.tagType) ?? .pain)
    }
    
    func getGoalById(_ goalid: UUID) -> LongTermGoal? {
        let request: NSFetchRequest<LongTermGoal> = LongTermGoal.fetchRequest()
        request.entity = LongTermGoal.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \LongTermGoal.id, ascending: true)]
        let allGoals = try! viewContext.fetch(request)
        for i in allGoals {
            if(i.id == goalid) {
                return i
            }
        }
        return nil
    }
    
    func triggerLongTermGoals() {
        for goal in goals {
            let request: NSFetchRequest<Activity> = Activity.fetchRequest()
            request.entity = Activity.entity()
            let p1 = NSPredicate(format: "tagType == %@", "pain")
            let p2 = NSPredicate(format: "type == %@", "task")
            let p3 = NSPredicate(format: "createdAt >= %@", goal.startTime as NSDate)
            let p4 = NSPredicate(format: "createdAt <= %@", goal.endTime as NSDate)
            let p5 = NSPredicate(format: "tagId == %@", goal.tagId as CVarArg)
            let predicate = NSCompoundPredicate(andPredicateWithSubpredicates: [p1, p2, p3, p4, p5])
            request.predicate = predicate
            let activitiesInPeriod = try! viewContext.fetch(request)
            goal.currentNo = Int16(activitiesInPeriod.count)
            
            if (Date() <= goal.endTime) {
                goal.status = "inprogress"
            }
            
            if (Date() >= goal.endTime && goal.currentNo < goal.targetNo) {
                goal.status = "failed"
            } else if (goal.currentNo >= goal.targetNo) {
                goal.status = "done"
                
                let newActivity = Activity(context: viewContext)
                newActivity.point = goal.point
                newActivity.tagName = "\(goal.targetNo) x \(getTagNameById(goal.tagId))"
                newActivity.createdAt = Date()
                newActivity.type = "goal"
                newActivity.id = UUID()
                newActivity.goalId = goal.id
                newActivity.tagType = "pain"
                actionActivity(act: newActivity)
                activities.insert(newActivity, at: 0)
            }
            
        }
        try! viewContext.save()
        loadGoals()
    }
    
    func loadMoreContent() {
        guard canLoadMorePages else {
            return
        }
        getActivitiesFromCD(currentOffset)
    }
    
    func getActivitiesFromCD(_ fetchOffSet: Int){
//        print("offset:", fetchOffSet)
        let request: NSFetchRequest<Activity> = Activity.fetchRequest()
        request.entity = Activity.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \Activity.createdAt, ascending: false)]
        request.fetchLimit = 10
        request.fetchOffset = currentOffset
        let newActList = try! viewContext.fetch(request)
        currentOffset += 10
        for i in newActList {
            activities.append(i)
        }
        if (newActList.count < 10) {
            canLoadMorePages = false
        }
    }
    
    func loadActivitiesByDate(date: Date) {
        activitiesByDate = []
        let dateFrom = calendar.startOfDay(for: date)
        let dateTo = calendar.date(byAdding: .day, value: 1, to: dateFrom)
        let request: NSFetchRequest<Activity> = Activity.fetchRequest()
        request.entity = Activity.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \Activity.createdAt, ascending: false)]
        request.predicate = NSPredicate(
            format: "(createdAt >= %@) AND (createdAt <= %@)", dateFrom as NSDate, dateTo! as NSDate
        )
        
        activitiesByDate = try! viewContext.fetch(request)
    }
    
    func getActivitiesInRange(date1: Date, date2: Date) -> [Activity] {
        activitiesByDate = []
        let dateFrom = calendar.startOfDay(for: date1)
        let dateToBefore = calendar.startOfDay(for: date2)
        let dateTo = calendar.date(byAdding: .day, value: 1, to: dateToBefore)
        let request: NSFetchRequest<Activity> = Activity.fetchRequest()
        request.entity = Activity.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \Activity.createdAt, ascending: false)]
        request.predicate = NSPredicate(
            format: "(createdAt >= %@) AND (createdAt <= %@)", dateFrom as NSDate, dateTo! as NSDate
        )
        
        return try! viewContext.fetch(request)
    }
    
    func loadActivitiesBySearch(text: String) {
        activitiesBySearch = []
        let request: NSFetchRequest<Activity> = Activity.fetchRequest()
        request.entity = Activity.entity()
        request.sortDescriptors = [ NSSortDescriptor(keyPath: \Activity.createdAt, ascending: false)]
        request.predicate = NSPredicate(
            format: "name LIKE %@", text
        )
        
        let res = try! viewContext.fetch(request)
        for i in res {
            activitiesBySearch.append(i)
        }
    }
    
    func updateBalance() {
        balance = 0
        let request: NSFetchRequest<Activity> = Activity.fetchRequest()
        request.entity = Activity.entity()
        let allacts = try! viewContext.fetch(request)
        for act in allacts {
            if (act.type == "goal") {
                balance += Int(act.point)
            } else if (act.type == "task") {
                if (act.tagType == "pain") {
                    balance += Int(act.point)
                } else if (act.tagType == "gain") {
                    balance -= Int(act.point)
                }
            }
        }
    }
    
    func actionActivity(act: Activity){
        updateBalance()
        updatePointNotifications(newPoint: Int16(balance))
    }
    
    func updatePointNotifications(newPoint : Int16) {
        getPointNotifications()
        if (!pointNotifications.isEmpty) {
            if (newPoint <= pointNotifications[0].point) {
                addNotification(
                    title: NSString.localizedUserNotificationString(forKey: "remaining_points", arguments: [newPoint]),
                    subtitle: "Getup and earn some points",
                    type: .point, notiTime: pointNotifications[0].triggertime, notiPoint: Int(pointNotifications[0].point))
            } else {
                NotificationHandler.shared.removeNotifications([pointNotifications[0].id.uuidString])
            }
        }
    }
    
    func revertActivity(act: Activity) {
        updateBalance()
        updatePointNotifications(newPoint: Int16(balance))
    }
    
    func getActivities() -> [Activity] {
        return activities
    }
    
    func addActivity(act: Activity) {
        activities.insert(act, at: 0)
        updateBalance()
        triggerLongTermGoals()
    }
    
    func deleteActivity(act: Activity) {
        for actLoop in activities {
            if actLoop.id == act.id {
                if let i = activities.firstIndex(of: actLoop) {
                    activities.remove(at: i)
                    break
                }
            }
        }
        viewContext.delete(act)
        try! viewContext.save()
        updateBalance()
        triggerLongTermGoals()
    }
    
    func deleteTag(tag: Tag) {
        for tagloop in tags {
            if tagloop.id == tag.id {
                if let i = tags.firstIndex(of: tagloop) {
                    tags.remove(at: i)
                    break
                }
            }
        }
        viewContext.delete(tag)
        try! viewContext.save()
    }
    
    func editTag(tagTobeEdited: Tag, name: String, point: Int) {
        tagTobeEdited.name = name
        tagTobeEdited.point = Int16(point)
        
        for tagloop in tags {
            if tagloop.id == tagTobeEdited.id {
                if let i = tags.firstIndex(of: tagloop) {
                    tags[i].name = tagTobeEdited.name
                    tags[i].point = tagTobeEdited.point
                    break
                }
            }
        }
        
        try! viewContext.save()
        
    }
    
    func deleteGoal(goal: LongTermGoal) {
        for goalLoop in goals {
            if goalLoop.id == goal.id {
                if let i = goals.firstIndex(of: goalLoop) {
                    goals.remove(at: i)
                    break
                }
            }
        }
        viewContext.delete(goal)
        try! viewContext.save()
        updateBalance()
    }
    
    func addNotification(title: String, subtitle: String, type: NotiType, notiTime: Date, notiPoint: Int) {
        updateBalance()
        switch type {
        case .point:
            getPointNotifications()
            if (pointNotifications.isEmpty) {
                let noti = LocalNotification(context: viewContext)
                noti.id = UUID()
                noti.type = type.rawValue
                noti.title = title
                noti.subtitle = subtitle
                noti.triggertime = notiTime
                noti.point = Int16(notiPoint)
                try! viewContext.save()
                pointNotifications = [noti]
                if (balance <= notiPoint) {
                    NotificationHandler.shared.addNotification(notif: noti)
                }
            } else {
                let noti = pointNotifications[0]
                let idTobeRemoved = noti.id
                noti.id = UUID()
                noti.type = type.rawValue
                noti.title = title
                noti.subtitle = subtitle
                noti.triggertime = notiTime
                noti.point = Int16(notiPoint)
                try! viewContext.save()
                NotificationHandler.shared.removeNotifications([idTobeRemoved.uuidString])
                pointNotifications = [noti]
                if (balance <= notiPoint) {
                    NotificationHandler.shared.addNotification(notif: noti)
                }
            }
        case .goal:
            print("todo")
        }
    }
    
    func getPointNotifications() {
        let request: NSFetchRequest<LocalNotification> = LocalNotification.fetchRequest()
        request.entity = LocalNotification.entity()
        request.predicate = NSPredicate(format: "type == %@", "point")
        pointNotifications = try! viewContext.fetch(request)
//        print("notis: \(pointNotifications)")
    }
    
    func clearPointNotification() {
        getPointNotifications()
        if (!pointNotifications.isEmpty) {
            NotificationHandler.shared.removeNotifications([pointNotifications[0].id.uuidString])
            viewContext.delete(pointNotifications[0])
            try! viewContext.save()
            pointNotifications = []
            NotificationHandler.shared.removeAllNotifications()
        }
    }
    
    func getAndUpdateVisitTime() -> Int {
        let request: NSFetchRequest<Visit> = Visit.fetchRequest()
        request.entity = Visit.entity()
        let visit = try! viewContext.fetch(request)
        if (visit.isEmpty) {
            let newVisit = Visit(context: viewContext)
            newVisit.count = 0
            try! viewContext.save()
            visitCount = 0
            return 0
        } else {
            visit[0].count += 1
            try! viewContext.save()
            visitCount = visit[0].count
            return Int(visit[0].count)
        }
    }
    
    func upgradeToVIP() {
        let request: NSFetchRequest<AppSetting> = AppSetting.fetchRequest()
        request.entity = AppSetting.entity()
        let appSettings = try! viewContext.fetch(request)
        
        if (appSettings.isEmpty) {
            let appSetting = AppSetting(context: viewContext)
            appSetting.isVIP = true
        } else {
            appSettings[0].isVIP = true
        }
        try! viewContext.save()
        isVIP = true
    }
    
    func getAppSettings() {
        let request: NSFetchRequest<AppSetting> = AppSetting.fetchRequest()
        request.entity = AppSetting.entity()
        let appSettings = try! viewContext.fetch(request)
        if (appSettings.isEmpty) {
            isVIP = false
        } else {
            isVIP = appSettings[0].isVIP
        }
    }
    
}
