//
//  NotificationHandler.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/18.
//

import Foundation
import UserNotifications

class NotificationHandler : NSObject, UNUserNotificationCenterDelegate{
    static let shared = NotificationHandler()
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response:
                                UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let notiName = Notification.Name(response.notification.request.identifier)

        NotificationCenter.default.post(name:notiName, object: response.notification.request.content)
        completionHandler()
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        
        let notiName = Notification.Name(notification.request.identifier)
    
        NotificationCenter.default.post(name:notiName, object: notification.request.content)
        completionHandler(.sound)
    }
}

extension NotificationHandler  {
    func requestPermission(_ delegate : UNUserNotificationCenterDelegate? = nil ,
                           onDeny handler :  (()-> Void)? = nil){
        
        let center = UNUserNotificationCenter.current()
        
        center.getNotificationSettings(completionHandler: { settings in
            if settings.authorizationStatus == .denied {
                if let handler = handler {
                    handler()
                }
                return
            }
            
            if settings.authorizationStatus != .authorized  {
                center.requestAuthorization(options: [.alert, .sound, .badge]) {
                    _ , error in
                    
                    if let error = error {
                        print("error handling \(error)")
                    }
                }
            }
            
        })
        center.delegate = delegate ?? self
    }

    func addNotification(notif: LocalNotification) {
        UNUserNotificationCenter.current().getPendingNotificationRequests(completionHandler: { notifications in
            print("notifications: \(notifications)")
        })
        
        let content = UNMutableNotificationContent()
        content.title = notif.title
        content.subtitle = NSString.localizedUserNotificationString(forKey: notif.subtitle, arguments: nil)
        
        content.sound = UNNotificationSound.default
        let dateComponents = Calendar.current.dateComponents([.hour, .minute], from: notif.triggertime)
//        print(dateComponents)
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        
        let request = UNNotificationRequest(identifier: notif.id.uuidString, content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request)
    }
    
    func removeNotifications(_ ids : [String]){
        UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: ids)
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ids)
    }
    
    func removeAllNotifications(){
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    
}
