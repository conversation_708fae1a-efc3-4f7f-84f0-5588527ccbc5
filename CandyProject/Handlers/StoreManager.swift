//
//  StoreManager.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/23.
//

import Foundation
import StoreKit

class StoreManager: NSObject, ObservableObject, SKProductsRequestDelegate, SKPaymentTransactionObserver {
    @Published var transactionState: SKPaymentTransactionState?
    @Published var myProducts = [SKProduct]()
    var dataSource : ContentDataSource
    var gVar : GlobalVar
    
    init (gVar : GlobalVar, dataSource : ContentDataSource) {
        self.gVar = gVar
        self.dataSource = dataSource
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchasing:
                transactionState = .purchasing
            case .purchased:
                dataSource.upgradeToVIP()
                queue.finishTransaction(transaction)
                transactionState = .purchased
                gVar.isLoading = false
            case .restored:
                print("purchase restored")
                dataSource.upgradeToVIP()
                queue.finishTransaction(transaction)
                transactionState = .restored
                gVar.isLoading = false
            case .failed, .deferred:
                queue.finishTransaction(transaction)
                transactionState = .failed
                print("purchase failed")
                gVar.isLoading = false
            default:
                queue.finishTransaction(transaction)
                gVar.isLoading = false
            }
        }
    }
    
    func purchaseProduct(product: SKProduct) {
        if SKPaymentQueue.canMakePayments() {
            gVar.isLoading = true
            let payment = SKPayment(product: product)
                    SKPaymentQueue.default().add(payment)
        } else {
            print("User can't make payment.")
        }
        
    }
    
    func restoreProducts() {
        print("Restoring products ...")
        gVar.isLoading = true
        SKPaymentQueue.default().restoreCompletedTransactions()
    }
    
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
//        print("Did receive response products \(response.products[0].price)")
        if !response.products.isEmpty {
            
            for fetchedProduct in response.products {
                DispatchQueue.main.async {
                    self.myProducts.append(fetchedProduct)
                }
            }
        }
        for invalidIdentifier in response.invalidProductIdentifiers {
            print("Invalid identifiers found: \(invalidIdentifier)")
        }
    }
    
    func request(_ request: SKRequest, didFailWithError error: Error) {
        print("Product Request did fail: \(error)")
    }
    
    func getProducts(productIDs: [String]) {
        print("Start requesting products ...")
        let request = SKProductsRequest(productIdentifiers: Set(productIDs))
        request.delegate = self
        request.start()
    }
    
}

extension SKProduct {

    private static let formatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        return formatter
    }()

    var isFree: Bool {
        price == 0.00
    }

    var localizedPrice: String? {
        guard !isFree else {
            return nil
        }
        
        let formatter = SKProduct.formatter
        formatter.locale = priceLocale

        return formatter.string(from: price)
    }

}
