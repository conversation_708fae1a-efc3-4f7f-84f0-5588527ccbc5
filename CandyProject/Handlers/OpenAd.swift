//
//  OpenAd.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/13.
//

import Foundation
import GoogleMobileAds
import SwiftUI

final class OpenAd: NSObject, GADFullScreenContentDelegate {
    @AppStorage("showLoadingScreen") var showLoadingScreen = true
    var appOpenAd: GADAppOpenAd?
    var loadTime = Date()
    @State var gVar : GlobalVar
    
    init(_ gvar: GlobalVar) {
        gVar = gvar
    }
    
    func requestAppOpenAd() {
        let request = GADRequest()
//        PROD: ca-app-pub-9142454212598964/8267252070
//        TEST: ca-app-pub-3940256099942544/5662855259
//        GADAppOpenAd.load(withAdUnitID: "ca-app-pub-9142454212598964/8267252070",
        GADAppOpenAd.load(withAdUnitID: "ca-app-pub-3940256099942544/5662855259",
                          request: request,
                          orientation: UIInterfaceOrientation.portrait,
                          completionHandler: { (appOpenAdIn, error) in
            if let error = error {
                print("load ad error:", error)
                self.gVar.showLoadingScreen = false
                return
            }
            print("load ad successfully")
            self.appOpenAd = appOpenAdIn
            self.appOpenAd?.fullScreenContentDelegate = self
            self.loadTime = Date()
            print("[OPEN AD] Ad is ready")
            self.appOpenAd?.present(fromRootViewController: (UIApplication.shared.currentUIWindow()?.rootViewController)!)
        })
    }
    
    func tryToPresentAd() {
        if let _ = self.appOpenAd, wasLoadTimeLessThanNHoursAgo(thresholdN: 1) {
            gVar.showLoadingScreen = false
        } else {
            self.requestAppOpenAd()
        }
    }
    
    func wasLoadTimeLessThanNHoursAgo(thresholdN: Int) -> Bool {
        let now = Date()
        let timeIntervalBetweenNowAndLoadTime = now.timeIntervalSince(self.loadTime)
        let secondsPerHour = 3600.0
        let intervalInHours = timeIntervalBetweenNowAndLoadTime / secondsPerHour
        return intervalInHours < Double(thresholdN)
    }
    
    func ad(_ ad: GADFullScreenPresentingAd, didFailToPresentFullScreenContentWithError error: Error) {
        print("[OPEN AD] Failed: \(error)")
        gVar.showLoadingScreen = false
        requestAppOpenAd()
    }
    
    func adDidDismissFullScreenContent(_ ad: GADFullScreenPresentingAd) {
        gVar.showLoadingScreen = false
        print("[OPEN AD] Ad dismissed \(gVar.showLoadingScreen)")
    }
    
    func adWillPresentFullScreenContent(_ ad: GADFullScreenPresentingAd) {
        print("[OPEN AD] Ad will present")
    }
    
}

public extension UIApplication {
    func currentUIWindow() -> UIWindow? {
        let connectedScenes = UIApplication.shared.connectedScenes
            .filter({
                $0.activationState == .foregroundActive})
            .compactMap({$0 as? UIWindowScene})
        
        let window = connectedScenes.first?
            .windows
            .first { $0.isKeyWindow }
        
        return window
        
    }
}
