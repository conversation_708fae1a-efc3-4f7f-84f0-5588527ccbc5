<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.2" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 http://docs.oasis-open.org/xliff/v1.2/os/xliff-core-1.2-strict.xsd">
  <file original="CandyProject/en.lproj/InfoPlist.strings" source-language="en" target-language="en" datatype="plaintext">
    <header>
      <tool tool-id="com.apple.dt.xcode" tool-name="Xcode" tool-version="13.3" build-num="13E113"/>
    </header>
    <body>
      <trans-unit id="CFBundleDisplayName" xml:space="preserve">
        <source>CandyYourself</source>
        <target>CandyYourself</target>
        <note>Bundle display name</note>
      </trans-unit>
      <trans-unit id="CFBundleName" xml:space="preserve">
        <source>CandyProject</source>
        <target>CandyProject</target>
        <note>Bundle name</note>
      </trans-unit>
    </body>
  </file>
  <file original="CandyProject/en.lproj/Localizable.strings" source-language="en" target-language="en" datatype="plaintext">
    <header>
      <tool tool-id="com.apple.dt.xcode" tool-name="Xcode" tool-version="13.3" build-num="13E113"/>
    </header>
    <body>
      <trans-unit id="%@" xml:space="preserve">
        <source>%@</source>
        <target>%@</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%@ %d" xml:space="preserve">
        <source>%@ %d</source>
        <target>%@ %d</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%@ - %@" xml:space="preserve">
        <source>%@ - %@</source>
        <target>%@ - %@</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d" xml:space="preserve">
        <source>%d</source>
        <target>%d</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d / %d" xml:space="preserve">
        <source>%d / %d</source>
        <target>%d / %d</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d x %@ + %d" xml:space="preserve">
        <source>%d x %@ + %d</source>
        <target>%d x %@ + %d</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Candy Name:" xml:space="preserve">
        <source>Candy Name:</source>
        <target>Candy Name:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Candy Price:" xml:space="preserve">
        <source>Candy Price:</source>
        <target>Candy Price:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Choose a Task:" xml:space="preserve">
        <source>Choose a Task:</source>
        <target>Choose a Task:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Candy" xml:space="preserve">
        <source>Create Candy</source>
        <target>Create Candy</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Goal" xml:space="preserve">
        <source>Create Goal</source>
        <target>Create Goal</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Task" xml:space="preserve">
        <source>Create Task</source>
        <target>Create Task</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Your Candy" xml:space="preserve">
        <source>Create Your Candy</source>
        <target>Create Your Candy</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Your Task" xml:space="preserve">
        <source>Create Your Task</source>
        <target>Create Your Task</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create your own Candy" xml:space="preserve">
        <source>Create your own Candy</source>
        <target>Create your own Candy</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create your own Task" xml:space="preserve">
        <source>Create your own Task</source>
        <target>Create your own Task</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="End Date:" xml:space="preserve">
        <source>End Date:</source>
        <target>End Date:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Go Create your own, and assign points to it" xml:space="preserve">
        <source>Go Create your own, and assign points to it</source>
        <target>Go Create your own, and assign points to it</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Number of Tasks:" xml:space="preserve">
        <source>Number of Tasks:</source>
        <target>Number of Tasks:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="OK" xml:space="preserve">
        <source>OK</source>
        <target>OK</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Point:" xml:space="preserve">
        <source>Point:</source>
        <target>Point:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="SET UP A GOAL" xml:space="preserve">
        <source>SET UP A GOAL</source>
        <target>SET UP A GOAL</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Start Date:" xml:space="preserve">
        <source>Start Date:</source>
        <target>Start Date:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task Name:" xml:space="preserve">
        <source>Task Name:</source>
        <target>Task Name:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task Point:" xml:space="preserve">
        <source>Task Point:</source>
        <target>Task Point:</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task cannot be deleted when it is used in a goal" xml:space="preserve">
        <source>Task cannot be deleted when it is used in a goal</source>
        <target>Task cannot be deleted when it is used in a goal</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="To create a goal, you need to go create a task first" xml:space="preserve">
        <source>To create a goal, you need to go create a task first</source>
        <target>To create a goal, you need to go create a task first</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="You have %@ %@ at %@ and %@ %@ points" xml:space="preserve">
        <source>You have %@ %@ at %@ and %@ %@ points</source>
        <target>You have %@ %@ at %@ and %@ %@ points</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="cancel" xml:space="preserve">
        <source>cancel</source>
        <target>cancel</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="click on the bottom plus button to earn some points" xml:space="preserve">
        <source>click on the bottom plus button to earn some points</source>
        <target>click on the bottom plus button to earn some points</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="click on the top candy button to redeem your candy" xml:space="preserve">
        <source>click on the top candy button to redeem your candy</source>
        <target>click on the top candy button to redeem your candy</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="edit" xml:space="preserve">
        <source>edit</source>
        <target>edit</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="eg. %@" xml:space="preserve">
        <source>eg. %@</source>
        <target>eg. %@</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no activities yet" xml:space="preserve">
        <source>no activities yet</source>
        <target>no activities yet</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no candy created yet" xml:space="preserve">
        <source>no candy created yet</source>
        <target>no candy created yet</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no task created yet" xml:space="preserve">
        <source>no task created yet</source>
        <target>no task created yet</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="save" xml:space="preserve">
        <source>save</source>
        <target>save</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
    </body>
  </file>
</xliff>
