<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.2" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 http://docs.oasis-open.org/xliff/v1.2/os/xliff-core-1.2-strict.xsd">
  <file original="CandyProject/en.lproj/InfoPlist.strings" source-language="en" target-language="zh-Hans" datatype="plaintext">
    <header>
      <tool tool-id="com.apple.dt.xcode" tool-name="Xcode" tool-version="13.3" build-num="13E113"/>
    </header>
    <body>
      <trans-unit id="CFBundleDisplayName" xml:space="preserve">
        <source>CandyYourself</source>
        <target>糖衣</target>
        <note>Bundle display name</note>
      </trans-unit>
      <trans-unit id="CFBundleName" xml:space="preserve">
        <source>CandyProject</source>
        <target>糖衣</target>
        <note>Bundle name</note>
      </trans-unit>
    </body>
  </file>
  <file original="CandyProject/en.lproj/Localizable.strings" source-language="en" target-language="zh-Hans" datatype="plaintext">
    <header>
      <tool tool-id="com.apple.dt.xcode" tool-name="Xcode" tool-version="13.3" build-num="13E113"/>
    </header>
    <body>
      <trans-unit id="%@" xml:space="preserve">
        <source>%@</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%@ %d" xml:space="preserve">
        <source>%@ %d</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%@ - %@" xml:space="preserve">
        <source>%@ - %@</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d" xml:space="preserve">
        <source>%d</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d / %d" xml:space="preserve">
        <source>%d / %d</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="%d x %@ + %d" xml:space="preserve">
        <source>%d x %@ + %d</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Candy Name:" xml:space="preserve">
        <source>Candy Name:</source>
        <target>糖果名称</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Candy Price:" xml:space="preserve">
        <source>Candy Price:</source>
        <target>糖果价格</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Choose a Task:" xml:space="preserve">
        <source>Choose a Task:</source>
        <target>选择一个任务</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Candy" xml:space="preserve">
        <source>Create Candy</source>
        <target>创建糖果</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Goal" xml:space="preserve">
        <source>Create Goal</source>
        <target>创建目标</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Task" xml:space="preserve">
        <source>Create Task</source>
        <target>创建任务</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Your Candy" xml:space="preserve">
        <source>Create Your Candy</source>
        <target>创建你的糖果</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create Your Task" xml:space="preserve">
        <source>Create Your Task</source>
        <target>创建你的任务</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create your own Candy" xml:space="preserve">
        <source>Create your own Candy</source>
        <target>创建属于你自己的糖果</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Create your own Task" xml:space="preserve">
        <source>Create your own Task</source>
        <target>创建属于你自己的任务</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="End Date:" xml:space="preserve">
        <source>End Date:</source>
        <target>结束日期：</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Go Create your own, and assign points to it" xml:space="preserve">
        <source>Go Create your own, and assign points to it</source>
        <target>去创建属于你自己的并且给予他一个分值吧</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Number of Tasks:" xml:space="preserve">
        <source>Number of Tasks:</source>
        <target>任务次数：</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="OK" xml:space="preserve">
        <source>OK</source>
        <target>OK</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Point:" xml:space="preserve">
        <source>Point:</source>
        <target>分值：</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="SET UP A GOAL" xml:space="preserve">
        <source>SET UP A GOAL</source>
        <target>设定一个目标</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Start Date:" xml:space="preserve">
        <source>Start Date:</source>
        <target>开始日期：</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task Name:" xml:space="preserve">
        <source>Task Name:</source>
        <target>任务名称</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task Point:" xml:space="preserve">
        <source>Task Point:</source>
        <target>任务点数</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="Task cannot be deleted when it is used in a goal" xml:space="preserve">
        <source>Task cannot be deleted when it is used in a goal</source>
        <target>被使用在正在进行中的目标中的任务不可删除</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="To create a goal, you need to go create a task first" xml:space="preserve">
        <source>To create a goal, you need to go create a task first</source>
        <target>你需要先创建至少一个任务来设置一个目标</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="You have %@ %@ at %@ and %@ %@ points" xml:space="preserve">
        <source>You have %@ %@ at %@ and %@ %@ points</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="cancel" xml:space="preserve">
        <source>cancel</source>
        <target>取消</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="click on the bottom plus button to earn some points" xml:space="preserve">
        <source>click on the bottom plus button to earn some points</source>
        <target>点击下方加号按钮获取积分</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="click on the top candy button to redeem your candy" xml:space="preserve">
        <source>click on the top candy button to redeem your candy</source>
        <target>点击上方糖果按钮兑换糖果</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="edit" xml:space="preserve">
        <source>edit</source>
        <target>编辑</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="eg. %@" xml:space="preserve">
        <source>eg. %@</source>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no activities yet" xml:space="preserve">
        <source>no activities yet</source>
        <target>当前没有记录</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no candy created yet" xml:space="preserve">
        <source>no candy created yet</source>
        <target>当前没有创建的糖果</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="no task created yet" xml:space="preserve">
        <source>no task created yet</source>
        <target>当前没有创建的任务</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
      <trans-unit id="save" xml:space="preserve">
        <source>save</source>
        <target>保存</target>
        <note>No comment provided by engineer.</note>
      </trans-unit>
    </body>
  </file>
</xliff>
