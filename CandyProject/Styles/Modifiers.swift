//
//  Modifiers.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/17.
//

import SwiftUI

struct ListModifier: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    var width: CGFloat = 300
    var height: CGFloat = 25
    var activityType: ActivityType = .task
    var actType: TagType = .pain
    var color: Color?
    var shadowColor: Color?
    var borderColor: Color = Color.gray
    
    func body(content: Content) -> some View {
        content
            .frame(width: width, height: height)
            .background(
                ZStack {
                    if (color != nil) {
                        color
                    } else {
                        actType == .pain ? Color("pink") : Color("green")
                    }
                }
            )
            .cornerRadius(10)
            .shadow(color: actType == .pain ? Color("green") : Color("pink"), radius: colorScheme == .dark ? 5 : 10)
            .modifier(OutlineModifier(cornerRadius: 10, color: borderColor))
    }
}

struct ButtonGradientModifier: ViewModifier {
//    @Environment(\.colorScheme) var colorScheme
    var height: CGFloat = 50
    var colorSet: [Color] = [Color("brightgreen"),Color("purple"),Color("orange"),Color("lightyellow")]
    var linearGradientColorSet = [Color(.systemBackground).opacity(0), Color(.systemBackground).opacity(1)]
    
    func body(content: Content) -> some View {
        content.frame(maxWidth: .infinity, maxHeight: 50)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.clear)
                        .overlay(AngularGradient(
                            gradient: Gradient(stops: [
                                .init(color: colorSet[0], location: 0.0),
                                .init(color: colorSet[1], location: 0.2),
                                .init(color: colorSet[2], location: 0.6),
                                .init(color: colorSet[3], location: 0.8)]),
                            center: .center
                        ))
                        .padding(6)
                        .blur(radius: 20)

                    LinearGradient(gradient: Gradient(colors: [Color(.systemBackground).opacity(1), Color(.systemBackground).opacity(0.6)]), startPoint: .top, endPoint: .bottom)
                        .cornerRadius(20)
                        .blendMode(.softLight)
                }
            )
            .frame(height: height)
            .accentColor(.primary.opacity(0.7))
    }
}

struct GradientModifier: ViewModifier {
//    @Environment(\.colorScheme) var colorScheme
    var height: CGFloat = 50
    var colorSet: [Color] = [Color("brightgreen"),Color("purple"),Color("orange"),Color("lightyellow")]
    var linearGradientColorSet = [Color(.systemBackground).opacity(0), Color(.systemBackground).opacity(1)]
    
    func body(content: Content) -> some View {
        content.frame(maxWidth: .infinity, maxHeight: 50)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.clear)
                        .overlay(AngularGradient(
                            gradient: Gradient(stops: [
                                .init(color: colorSet[0], location: 0.0),
                                .init(color: colorSet[1], location: 0.2),
                                .init(color: colorSet[2], location: 0.6),
                                .init(color: colorSet[3], location: 0.8)]),
                            center: .center
                        ))
                        .padding(6)
                        .blur(radius: 20)

                    LinearGradient(gradient: Gradient(colors: [Color.white.opacity(0), Color.white.opacity(0.5)]), startPoint: .top, endPoint: .bottom)
                        .blendMode(.softLight)
                }
            )
            .frame(height: height)
            .accentColor(.primary.opacity(0.7))
    }
}

struct OutlineModifier: ViewModifier {
//    @Environment(\.colorScheme) var colorScheme
    var cornerRadius: CGFloat = 20
    var color: Color = Color.gray
    
    func body(content: Content) -> some View {
        content.overlay(
            RoundedRectangle(cornerRadius: cornerRadius)
            .stroke(
                color
            )
        )
    }
}

struct FormLabelModifier: ViewModifier {
    func body(content: Content) -> some View {
        content.font(.footnote)
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.leading, 10)
    }
}

struct RoundedRectOutlinedBgModifier: ViewModifier {
    var fillcolor: Color = .white
    var bordercolor: Color = .white
    var op: CGFloat = 0.3
    var corner: CGFloat = 0
    var strokeWidth: CGFloat = 1
    
    func body(content: Content) -> some View {
        content.background(
            RoundedRectangle(cornerRadius: corner)
                .fill(
                    fillcolor
                )
                .opacity(op)
        )
        .overlay(
            RoundedRectangle(cornerRadius: corner)
                .stroke(
                    bordercolor, lineWidth: strokeWidth
                )
                
        )
    }
    

}



