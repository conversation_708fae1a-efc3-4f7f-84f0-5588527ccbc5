//
//  DesignSystem.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI

// MARK: - Modern Color System
extension Color {
    // Primary Colors
    static let primaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let primaryPurple = Color(red: 0.55, green: 0.27, blue: 0.95)
    static let primaryGreen = Color(red: 0.20, green: 0.78, blue: 0.35)
    static let primaryOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let primaryPink = Color(red: 1.0, green: 0.18, blue: 0.33)
    
    // Background Colors
    static let backgroundPrimary = Color(.systemBackground)
    static let backgroundSecondary = Color(.secondarySystemBackground)
    static let backgroundTertiary = Color(.tertiarySystemBackground)
    
    // Surface Colors
    static let surfaceElevated = Color(.systemBackground)
    static let surfaceCard = Color(.secondarySystemBackground)
    
    // Text Colors
    static let textPrimary = Color(.label)
    static let textSecondary = Color(.secondaryLabel)
    static let textTertiary = Color(.tertiaryLabel)
    
    // Task & Candy Colors
    static let taskGreen = Color(red: 0.20, green: 0.78, blue: 0.35)
    static let taskGreenLight = Color(red: 0.20, green: 0.78, blue: 0.35).opacity(0.1)
    static let candyPink = Color(red: 1.0, green: 0.18, blue: 0.33)
    static let candyPinkLight = Color(red: 1.0, green: 0.18, blue: 0.33).opacity(0.1)
    
    // Accent Colors
    static let accentBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let accentPurple = Color(red: 0.55, green: 0.27, blue: 0.95)
}

// MARK: - Typography System
struct Typography {
    static let largeTitle = Font.largeTitle.weight(.bold)
    static let title1 = Font.title.weight(.semibold)
    static let title2 = Font.title2.weight(.semibold)
    static let title3 = Font.title3.weight(.medium)
    static let headline = Font.headline.weight(.semibold)
    static let body = Font.body
    static let bodyMedium = Font.body.weight(.medium)
    static let callout = Font.callout
    static let subheadline = Font.subheadline
    static let footnote = Font.footnote
    static let caption = Font.caption
}

// MARK: - Spacing System
struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
}

// MARK: - Corner Radius System
struct CornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 20
    static let xxl: CGFloat = 24
}

// MARK: - Shadow System
struct ShadowStyle {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
    
    static let small = ShadowStyle(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    static let medium = ShadowStyle(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
    static let large = ShadowStyle(color: .black.opacity(0.2), radius: 16, x: 0, y: 8)
}

// MARK: - Modern Card Modifier
struct ModernCardModifier: ViewModifier {
    let backgroundColor: Color
    let cornerRadius: CGFloat
    let shadow: ShadowStyle
    
    init(
        backgroundColor: Color = .surfaceCard,
        cornerRadius: CGFloat = CornerRadius.lg,
        shadow: ShadowStyle = .medium
    ) {
        self.backgroundColor = backgroundColor
        self.cornerRadius = cornerRadius
        self.shadow = shadow
    }
    
    func body(content: Content) -> some View {
        content
            .background(backgroundColor)
            .cornerRadius(cornerRadius)
            .shadow(
                color: shadow.color,
                radius: shadow.radius,
                x: shadow.x,
                y: shadow.y
            )
    }
}

// MARK: - Modern Button Styles
struct PrimaryButtonStyle: ButtonStyle {
    let backgroundColor: Color
    let foregroundColor: Color
    
    init(backgroundColor: Color = .primaryBlue, foregroundColor: Color = .white) {
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(Typography.bodyMedium)
            .foregroundColor(foregroundColor)
            .padding(.horizontal, Spacing.lg)
            .padding(.vertical, Spacing.md)
            .background(backgroundColor)
            .cornerRadius(CornerRadius.lg)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    let borderColor: Color
    let foregroundColor: Color
    
    init(borderColor: Color = .primaryBlue, foregroundColor: Color = .primaryBlue) {
        self.borderColor = borderColor
        self.foregroundColor = foregroundColor
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(Typography.bodyMedium)
            .foregroundColor(foregroundColor)
            .padding(.horizontal, Spacing.lg)
            .padding(.vertical, Spacing.md)
            .background(Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(borderColor, lineWidth: 2)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Task/Candy Item Modifier
struct TaskCandyItemModifier: ViewModifier {
    let type: TagType
    let isSelected: Bool
    
    var backgroundColor: Color {
        switch type {
        case .pain:
            return isSelected ? .taskGreen : .taskGreenLight
        case .gain:
            return isSelected ? .candyPink : .candyPinkLight
        }
    }
    
    var borderColor: Color {
        switch type {
        case .pain:
            return .taskGreen
        case .gain:
            return .candyPink
        }
    }
    
    func body(content: Content) -> some View {
        content
            .padding(Spacing.md)
            .background(backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .stroke(borderColor, lineWidth: isSelected ? 2 : 1)
            )
            .cornerRadius(CornerRadius.md)
    }
}

// MARK: - Navigation Bar Modifier
struct ModernNavigationBarModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.topLeft, .topRight])
    }
}

// MARK: - Helper Extensions
extension View {
    func modernCard(
        backgroundColor: Color = .surfaceCard,
        cornerRadius: CGFloat = CornerRadius.lg,
        shadow: ShadowStyle = .medium
    ) -> some View {
        modifier(ModernCardModifier(backgroundColor: backgroundColor, cornerRadius: cornerRadius, shadow: shadow))
    }
    
    func taskCandyItem(type: TagType, isSelected: Bool = false) -> some View {
        modifier(TaskCandyItemModifier(type: type, isSelected: isSelected))
    }
    
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
