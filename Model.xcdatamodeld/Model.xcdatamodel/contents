<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="19574" systemVersion="20E241" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Activity" representedClassName="Activity" syncable="YES" codeGenerationType="class">
        <attribute name="created" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="tagid" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
    </entity>
    <entity name="Tag" representedClassName="Tag" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="point" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String"/>
    </entity>
    <elements>
        <element name="Tag" positionX="-63" positionY="-18" width="128" height="89"/>
        <element name="Activity" positionX="-54" positionY="18" width="128" height="74"/>
    </elements>
</model>