Arguments: 
  /usr/local/Cellar/node/19.1.0/bin/node /usr/local/Cellar/yarn/1.5.1_1/libexec/bin/yarn.js run commit

PATH: 
  /usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/usr/local/go/bin:/Users/<USER>/.rbenv/shims:/usr/local/sbin:/opt/homebrew/sbin:/opt/homebrew/bin:/Users/<USER>/.rvm/gems/ruby-3.1.2/bin:/Users/<USER>/.rvm/gems/ruby-3.1.2@global/bin:/Users/<USER>/.rvm/rubies/ruby-3.1.2/bin:/Users/<USER>/.gvm/bin:/Users/<USER>/.nvm/versions/node/v16.14.1/bin:/anaconda3/bin:/Library/Frameworks/Python.framework/Versions/3.6/bin:/Users/<USER>/.rbenv/plugins/ruby-build/bin:/Users/<USER>/.rbenv/bin:/Users/<USER>/.cargo/bin:/usr/local/mysql/bin:/opt/local/bin:/usr/local/gnat/bin:/Users/<USER>/.rvm/bin:/usr/local/bin:/usr/bin:/bin

Yarn version: 
  1.5.1

Node version: 
  19.1.0

Platform: 
  darwin x64

npm manifest: 
  No manifest

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile

Trace: 
  Error: Couldn't find a package.json file in "/Users/<USER>/Desktop/programming/CandyProject"
      at new MessageError (/usr/local/Cellar/yarn/1.5.1_1/libexec/lib/cli.js:186:110)
      at /usr/local/Cellar/yarn/1.5.1_1/libexec/lib/cli.js:40048:15
      at Generator.next (<anonymous>)
      at step (/usr/local/Cellar/yarn/1.5.1_1/libexec/lib/cli.js:98:30)
      at /usr/local/Cellar/yarn/1.5.1_1/libexec/lib/cli.js:109:13
