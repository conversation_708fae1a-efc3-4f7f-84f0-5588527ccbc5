//
//  CandyWidget.swift
//  CandyWidget
//
//  Created by <PERSON><PERSON> on 2022/5/10.
//

import WidgetKit
import SwiftUI
import Intents

struct Provider: IntentTimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), configuration: ConfigurationIntent())
    }
    
    func getSnapshot(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), configuration: configuration)
        completion(entry)
    }
    
    func getTimeline(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [SimpleEntry] = []
        
        // Generate a timeline consisting of five entries an hour apart, starting from the current date.
        let currentDate = Date()
        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SimpleEntry(date: entryDate, configuration: configuration)
            entries.append(entry)
        }
        
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationIntent
}

struct CandyWidgetEntryView : View {
    @Environment(\.widgetFamily) var size
    var entry: Provider.Entry
    
    var small: some View {
        Image("candy")
            .resizable(resizingMode: .stretch)
            .aspectRatio(contentMode: .fit)
            .frame(width: 120.0, height: 60.0)
            .shadow(color: Color.black.opacity(0.5), radius: 10)
            .overlay(
                Text("120")
                    .foregroundColor(Color.black)
                    .shadow(color: Color.black, radius: 10.0)
            )
    }
    var body: some View {
        
        switch (size) {
        case .systemSmall:
            small
        case .systemMedium:
            small
        case .systemLarge:
            small
        case .systemExtraLarge:
            small
        @unknown default:
            small
        }        }
    //            Image("candy")
    //                .resizable(resizingMode: .stretch)
    //                .aspectRatio(contentMode: .fit)
    //                .frame(width: 120.0, height: 60.0)
    //                .shadow(color: Color.black.opacity(0.5), radius: 10)
    //                .overlay(
    //                    Text("120")
    //                        .foregroundColor(Color.black)
    //                        .shadow(color: Color.black, radius: 10.0)
    //                )
    
}

//struct SmallWidgetEntryView : View {
//
//}

@main
struct CandyWidget: Widget {
    let kind: String = "CandyWidget"
    
    var body: some WidgetConfiguration {
        IntentConfiguration(kind: kind, intent: ConfigurationIntent.self, provider: Provider()) { entry in
            CandyWidgetEntryView(entry: entry)
        }
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
        .configurationDisplayName("My Widget")
        .description("This is an example widget.")
    }
}

struct CandyWidget_Previews: PreviewProvider {
    static var previews: some View {
        CandyWidgetEntryView(entry: SimpleEntry(date: Date(), configuration: ConfigurationIntent()))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
    }
}
