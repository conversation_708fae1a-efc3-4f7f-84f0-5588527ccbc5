//
//  RemainingPoint+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/28.
//
//

import Foundation
import CoreData


extension RemainingPoint {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<RemainingPoint> {
        return NSFetchRequest<RemainingPoint>(entityName: "RemainingPoint")
    }

    @NSManaged public var point: Int16
    @NSManaged public var id: UUID?

}

extension RemainingPoint : Identifiable {

}
