//
//  Tag+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/18.
//
//

import Foundation
import CoreData


extension Tag {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Tag> {
        return NSFetchRequest<Tag>(entityName: "Tag")
    }
    
    @NSManaged public var type: String
    @NSManaged public var id: UUID
    @NSManaged public var point: Int16
    @NSManaged public var name: String
    @NSManaged public var orderIndex: Int16
    
    var tagType: TagType {
        get {
            let type = self.type
            return TagType(rawValue: type)!   
        }
        set {
            self.type = newValue.rawValue
        }
    }

}

extension Tag : Identifiable {

}

enum TagType: String {
    case pain = "pain"
    case gain = "gain"
}
