//
//  Activity+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/18.
//
//

import Foundation
import CoreData


extension Activity {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Activity> {
        return NSFetchRequest<Activity>(entityName: "Activity")
    }

    @NSManaged public var createdAt: Date
    @NSManaged public var id: UUID
    @NSManaged public var tagName: String
    @NSManaged public var tagType: String
    @NSManaged public var note: String?
    @NSManaged public var point: Int16
    @NSManaged public var tagId: UUID?
    @NSManaged public var goalId: UUID?
    @NSManaged public var type: String
    
    var newTagType: TagType {
        get {
            let tagType = self.tagType
            return TagType(rawValue: tagType)!
        }
        set {
            self.tagType = newValue.rawValue
        }
    }
    
    var newType: ActivityType {
        get {
            let type = self.type
            return ActivityType(rawValue: type)!
        }
        set {
            self.type = newValue.rawValue
        }
    }
}


extension Activity : Identifiable {

}

enum ActivityType : String {
    case task = "task"
    case goal = "goal"
}
