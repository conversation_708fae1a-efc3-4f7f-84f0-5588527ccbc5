//
//  AppSetting+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/23.
//
//

import Foundation
import CoreData


extension AppSetting {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<AppSetting> {
        return NSFetchRequest<AppSetting>(entityName: "AppSetting")
    }

    @NSManaged public var isVIP: Bool

}

extension AppSetting : Identifiable {

}
