//
//  Wish+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/7.
//
//

import Foundation
import CoreData


extension Wish {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Wish> {
        return NSFetchRequest<Wish>(entityName: "Wish")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var point: Int16

}

extension Wish : Identifiable {

}
