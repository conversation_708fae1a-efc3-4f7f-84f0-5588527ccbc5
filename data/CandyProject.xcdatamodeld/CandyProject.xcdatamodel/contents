<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="20086" systemVersion="21E258" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Activity" representedClassName="Activity" syncable="YES">
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="goalId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="note" optional="YES" attributeType="String"/>
        <attribute name="point" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="tagId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="tagName" optional="YES" attributeType="String"/>
        <attribute name="tagType" optional="YES" attributeType="String"/>
        <attribute name="type" optional="YES" attributeType="String"/>
    </entity>
    <entity name="AppSetting" representedClassName="AppSetting" syncable="YES">
        <attribute name="isVIP" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    </entity>
    <entity name="LocalNotification" representedClassName="LocalNotification" syncable="YES">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="point" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="subtitle" attributeType="String" defaultValueString=""/>
        <attribute name="title" attributeType="String" defaultValueString=""/>
        <attribute name="triggertime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="type" optional="YES" attributeType="String"/>
    </entity>
    <entity name="LongTermGoal" representedClassName="LongTermGoal" syncable="YES">
        <attribute name="currentNo" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="endTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="point" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="startTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="status" optional="YES" attributeType="String"/>
        <attribute name="tagId" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="targetNo" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="totalNumber" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="RemainingPoint" representedClassName="RemainingPoint" syncable="YES">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="point" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="Tag" representedClassName="Tag" syncable="YES">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String" defaultValueString=""/>
        <attribute name="orderIndex" optional="YES" attributeType="Integer 16" usesScalarValueType="YES"/>
        <attribute name="point" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String"/>
    </entity>
    <entity name="Visit" representedClassName="Visit" syncable="YES">
        <attribute name="count" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <configuration name="Cloud" usedWithCloudKit="YES">
        <memberEntity name="Activity"/>
        <memberEntity name="AppSetting"/>
        <memberEntity name="LocalNotification"/>
        <memberEntity name="LongTermGoal"/>
        <memberEntity name="RemainingPoint"/>
        <memberEntity name="Tag"/>
        <memberEntity name="Visit"/>
    </configuration>
    <configuration name="Local">
        <memberEntity name="Activity"/>
        <memberEntity name="AppSetting"/>
        <memberEntity name="LocalNotification"/>
        <memberEntity name="LongTermGoal"/>
        <memberEntity name="RemainingPoint"/>
        <memberEntity name="Tag"/>
        <memberEntity name="Visit"/>
    </configuration>
    <elements>
        <element name="Activity" positionX="-45" positionY="0" width="128" height="164"/>
        <element name="AppSetting" positionX="-9" positionY="108" width="128" height="44"/>
        <element name="LocalNotification" positionX="-27" positionY="90" width="128" height="119"/>
        <element name="LongTermGoal" positionX="-27" positionY="45" width="128" height="164"/>
        <element name="RemainingPoint" positionX="-27" positionY="90" width="128" height="59"/>
        <element name="Tag" positionX="-18" positionY="27" width="128" height="104"/>
        <element name="Visit" positionX="-18" positionY="99" width="128" height="44"/>
    </elements>
</model>