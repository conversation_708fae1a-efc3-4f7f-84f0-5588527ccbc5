//
//  LocalNotification+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/4/19.
//
//

import Foundation
import CoreData


extension LocalNotification {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<LocalNotification> {
        return NSFetchRequest<LocalNotification>(entityName: "LocalNotification")
    }

    @NSManaged public var id: UUID
    @NSManaged public var title: String
    @NSManaged public var subtitle: String
    @NSManaged public var triggertime: Date
    @NSManaged public var type: String
    @NSManaged public var point: Int16

    var newType: NotiType {
        get {
            let type = self.type
            return NotiType(rawValue: type)!
        }
        set {
            self.type = newValue.rawValue
        }
    }
}

extension LocalNotification : Identifiable {

}

enum NotiType: String {
    case point = "point"
    case goal = "goal"
}
