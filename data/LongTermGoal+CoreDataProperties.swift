//
//  LongTermGoal+CoreDataProperties.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/20.
//
//

import Foundation
import CoreData


extension LongTermGoal {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<LongTermGoal> {
        return NSFetchRequest<LongTermGoal>(entityName: "LongTermGoal")
    }

    @NSManaged public var startTime: Date
    @NSManaged public var endTime: Date
    @NSManaged public var targetNo: Int16
    @NSManaged public var currentNo: Int16
    @NSManaged public var tagId: UUID
    @NSManaged public var status: String
    @NSManaged public var id: UUID
    @NSManaged public var point: Int16
    
    var goalStatus: GoalStatus {
        get {
            let status = self.status
            return GoalStatus(rawValue: status)!
        }
        set {
            self.status = newValue.rawValue
        }
    }
}

extension LongTermGoal : Identifiable {

}

enum GoalStatus : String{
    case inprogress = "inprogress"
    case done = "done"
    case failed = "failed"
}
